import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../controllers/movie_controller.dart';
import '../../models/movie_model.dart';
import '../../utils/genre_utils.dart';
import '../../utils/app_colors.dart';
import 'movie_detail_page.dart';

class AllMoviesPage extends StatefulWidget {
  final String? genre;

  const AllMoviesPage({Key? key, this.genre}) : super(key: key);

  @override
  State<AllMoviesPage> createState() => _AllMoviesPageState();
}

class _AllMoviesPageState extends State<AllMoviesPage> {
  final MovieController _movieController = Get.find<MovieController>();
  final ScrollController _scrollController = ScrollController();
  String? _selectedGenre;

  @override
  void initState() {
    super.initState();
    _selectedGenre = widget.genre?.isNotEmpty == true ? widget.genre : null;
    // Load bookable movies when page initializes
    _movieController.fetchBookableMovies();
  }

  List<Movie> _getFilteredMovies() {
    // Use bookable movies instead of all movies
    final bookableMovies = _movieController.bookableMovies;
    if (_selectedGenre == null) return bookableMovies;

    return bookableMovies.where((movie) {
      return GenreUtils.matchesGenre(movie.genres, _selectedGenre!);
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: AppColors.primaryGradientVertical,
        ),
        child: SafeArea(
          child: Column(
            children: [
              // Header
              Padding(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    IconButton(
                      onPressed: () => Get.back(),
                      icon: const Icon(
                        Icons.arrow_back_ios,
                        color: Colors.white,
                      ),
                    ),
                    Expanded(
                      child: Text(
                        _selectedGenre != null
                            ? 'Phim $_selectedGenre có thể đặt vé'
                            : 'Phim có thể đặt vé',
                        style: GoogleFonts.mulish(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                    ),
                    IconButton(
                      onPressed: () {
                        Get.toNamed('/search');
                      },
                      icon: const Icon(
                        Icons.search,
                        color: Colors.white,
                      ),
                    ),
                  ],
                ),
              ),

              // Genre Filter
              Container(
                height: 50,
                margin: const EdgeInsets.symmetric(horizontal: 16),
                child: ListView.builder(
                  scrollDirection: Axis.horizontal,
                  itemCount:
                      GenreUtils.allGenres.length + 1, // +1 for "All" option
                  itemBuilder: (context, index) {
                    if (index == 0) {
                      // "All" option
                      final isSelected = _selectedGenre == null;
                      return _buildGenreChip('Tất cả', isSelected, () {
                        setState(() {
                          _selectedGenre = null;
                        });
                      });
                    }

                    final genre = GenreUtils.allGenres[index - 1];
                    final isSelected = _selectedGenre == genre;
                    return _buildGenreChip(genre, isSelected, () {
                      setState(() {
                        _selectedGenre = genre;
                      });
                    });
                  },
                ),
              ),

              const SizedBox(height: 16),

              // Movies Grid
              Expanded(
                child: Obx(() {
                  final isLoading =
                      _movieController.isLoadingFirebaseMovies.value;
                  final filteredMovies = _getFilteredMovies();

                  if (isLoading) {
                    return _buildLoadingState();
                  }

                  if (filteredMovies.isEmpty) {
                    return _buildEmptyState();
                  }

                  return _buildMoviesGrid(filteredMovies);
                }),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildGenreChip(String label, bool isSelected, VoidCallback onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        margin: const EdgeInsets.only(right: 8),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          gradient: isSelected ? AppColors.buttonGradient : null,
          color: isSelected ? null : Colors.white.withOpacity(0.1),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: isSelected
                ? AppColors.primaryGradientStart
                : Colors.white.withOpacity(0.3),
            width: 1,
          ),
        ),
        child: Text(
          label,
          style: GoogleFonts.mulish(
            color: isSelected ? Colors.white : Colors.white,
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
            fontSize: 14,
          ),
        ),
      ),
    );
  }

  Widget _buildLoadingState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            color: AppColors.primaryGradientStart,
            strokeWidth: 3,
          ),
          SizedBox(height: 16),
          Text(
            'Đang tải phim...',
            style: TextStyle(
              color: Colors.white70,
              fontSize: 16,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.movie_outlined,
            size: 80,
            color: Colors.white.withOpacity(0.3),
          ),
          const SizedBox(height: 16),
          Text(
            _selectedGenre != null
                ? 'Không có phim $_selectedGenre có thể đặt vé'
                : 'Không có phim nào có thể đặt vé',
            style: GoogleFonts.mulish(
              fontSize: 18,
              color: Colors.white70,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Thử chọn thể loại khác hoặc kiểm tra lại sau khi có lịch chiếu mới',
            style: GoogleFonts.mulish(
              fontSize: 14,
              color: Colors.white54,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildMoviesGrid(List<Movie> movies) {
    return GridView.builder(
      controller: _scrollController,
      padding: const EdgeInsets.all(16),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 0.7,
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
      ),
      itemCount: movies.length,
      itemBuilder: (context, index) {
        final movie = movies[index];
        return _buildMovieCard(movie);
      },
    );
  }

  Widget _buildMovieCard(Movie movie) {
    return GestureDetector(
      onTap: () => Get.to(() => MovieDetailsPage(movieId: movie.id)),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          color: Colors.white.withOpacity(0.1),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.2),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Movie Poster
            Expanded(
              flex: 3,
              child: ClipRRect(
                borderRadius: const BorderRadius.vertical(
                  top: Radius.circular(12),
                ),
                child: Image.network(
                  movie.fullPosterPath,
                  width: double.infinity,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    return Container(
                      color: Colors.grey[800],
                      child: const Center(
                        child: Icon(
                          Icons.movie,
                          color: Colors.white54,
                          size: 40,
                        ),
                      ),
                    );
                  },
                ),
              ),
            ),

            // Movie Info
            Expanded(
              flex: 1,
              child: Padding(
                padding: const EdgeInsets.all(8),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      movie.title,
                      style: GoogleFonts.mulish(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 4),
                    if (movie.voteAverage != null)
                      Row(
                        children: [
                          const Icon(
                            Icons.star,
                            color: AppColors.primaryGradientStart,
                            size: 14,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            movie.rating,
                            style: GoogleFonts.mulish(
                              fontSize: 12,
                              color: Colors.white70,
                            ),
                          ),
                        ],
                      ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
