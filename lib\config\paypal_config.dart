class PayPalConfig {
  static const String sandboxClientId =
      'AX2fmdCPYyB24TsYsZAf-VbOonTGOi9wfS86KmabwgBZp897S5SoCpKru4GT4ZTObmb4qAg67KJwToiO';
  static const String sandboxSecretKey =
      'EOumV9HjGcIfvWFbBdvUBNVkkm-UQT6POlx4BR3mk8PEk8lnsQiA05Z1Rk3OcRM0sardEdmiBQUwBQqb';

  static const String productionClientId = 'YOUR_PRODUCTION_CLIENT_ID_HERE';
  static const String productionSecretKey = 'YOUR_PRODUCTION_SECRET_KEY_HERE';

  static const bool useSandbox = true;

  static String get clientId =>
      useSandbox ? sandboxClientId : productionClientId;

  static String get secretKey =>
      useSandbox ? sandboxSecretKey : productionSecretKey;

  static bool get isConfigured {
    final currentClientId = clientId;
    final currentSecretKey = secretKey;

    if (useSandbox) {
      return currentClientId.isNotEmpty && currentSecretKey.isNotEmpty;
    } else {
      return currentClientId.isNotEmpty &&
          currentSecretKey.isNotEmpty &&
          currentClientId != 'YOUR_PRODUCTION_CLIENT_ID_HERE' &&
          currentSecretKey != 'YOUR_PRODUCTION_SECRET_KEY_HERE';
    }
  }


  static String get environmentName => useSandbox ? 'Sandbox' : 'Production';

  static const String setupInstructions = '''
Để thiết lập tích hợp PayPal:

1. Truy cập https://developer.paypal.com/
2. Đăng nhập bằng tài khoản PayPal của bạn
3. Tạo ứng dụng mới trong môi trường sandbox
4. Sao chép Client ID và Secret Key
5. Thay thế các giá trị trong lib/config/paypal_config.dart:
   - sandboxClientId: Client ID sandbox của bạn
   - sandboxSecretKey: Secret key sandbox của bạn

Để test, bạn có thể sử dụng tài khoản test PayPal sandbox:
- Tài khoản buyer: Tạo tài khoản sandbox cá nhân
- Tài khoản seller: Tạo tài khoản sandbox doanh nghiệp

Số thẻ tín dụng test:
- Visa: ****************
- MasterCard: ****************
- American Express: ***************
''';
}
