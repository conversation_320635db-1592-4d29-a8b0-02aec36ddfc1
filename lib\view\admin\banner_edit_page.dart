import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../controllers/banner_controller.dart';
import '../../models/banner_model.dart';

class BannerEditPage extends StatefulWidget {
  final BannerModel? banner;

  const BannerEditPage({
    Key? key,
    this.banner,
  }) : super(key: key);

  @override
  State<BannerEditPage> createState() => _BannerEditPageState();
}

class _BannerEditPageState extends State<BannerEditPage> {
  final _formKey = GlobalKey<FormState>();

  late TextEditingController _titleController;
  late TextEditingController _imageUrlController;
  late TextEditingController _descriptionController;

  late BannerType _selectedType;
  late bool _isActive;

  final BannerController _bannerController = Get.find<BannerController>();

  bool _isEditing = false;
  String _errorMessage = '';

  @override
  void initState() {
    super.initState();

    _isEditing = widget.banner != null;

    // Initialize controllers with existing data or empty strings
    _titleController = TextEditingController(
      text: widget.banner?.title ?? '',
    );

    _imageUrlController = TextEditingController(
      text: widget.banner?.imageUrl ?? '',
    );

    _descriptionController = TextEditingController(
      text: widget.banner?.description ?? '',
    );

    _selectedType = widget.banner?.type ?? BannerType.home;
    _isActive = widget.banner?.isActive ?? true;
  }

  @override
  void dispose() {
    _titleController.dispose();
    _imageUrlController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  Future<void> _saveBanner() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _errorMessage = '';
    });

    try {
      final now = DateTime.now();

      if (_isEditing) {
        // Update existing banner
        final updatedBanner = widget.banner!.copyWith(
          title: _titleController.text.trim(),
          imageUrl: _imageUrlController.text.trim(),
          description: _descriptionController.text.trim(),
          type: _selectedType,
          isActive: _isActive,
          updatedAt: now,
        );

        final success = await _bannerController.updateBanner(updatedBanner);

        if (success) {
          Get.back(result: true);
          Get.snackbar(
            'Success',
            'Banner updated successfully',
            snackPosition: SnackPosition.BOTTOM,
          );
        } else {
          setState(() {
            _errorMessage = _bannerController.errorMessage.value;
          });
        }
      } else {
        // Create new banner
        final newBanner = BannerModel(
          id: '', // Will be set by Firestore
          title: _titleController.text.trim(),
          imageUrl: _imageUrlController.text.trim(),
          description: _descriptionController.text.trim(),
          type: _selectedType,
          isActive: _isActive,
          createdAt: now,
          updatedAt: now,
        );

        final success = await _bannerController.addBanner(newBanner);

        if (success) {
          Get.back(result: true);
          Get.snackbar(
            'Success',
            'Banner added successfully',
            snackPosition: SnackPosition.BOTTOM,
          );
        } else {
          setState(() {
            _errorMessage = _bannerController.errorMessage.value;
          });
        }
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Error: ${e.toString()}';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_isEditing ? 'Edit Banner' : 'Add Banner'),
        centerTitle: true,
      ),
      body: SafeArea(
        child: Obx(() {
          final isSubmitting = _bannerController.isSubmitting.value;

          return SingleChildScrollView(
            padding: const EdgeInsets.all(16.0),
            child: Form(
              key: _formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Error message
                  if (_errorMessage.isNotEmpty)
                    Container(
                      padding: const EdgeInsets.all(12),
                      margin: const EdgeInsets.only(bottom: 16),
                      decoration: BoxDecoration(
                        color: Colors.red.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Text(
                        _errorMessage,
                        style: const TextStyle(
                          color: Colors.red,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),

                  // Banner preview
                  if (_imageUrlController.text.isNotEmpty)
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'Preview:',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                          ),
                        ),
                        const SizedBox(height: 8),
                        ClipRRect(
                          borderRadius: BorderRadius.circular(12),
                          child: AspectRatio(
                            aspectRatio: 16 / 9,
                            child: Image.network(
                              _imageUrlController.text,
                              fit: BoxFit.cover,
                              errorBuilder: (context, error, stackTrace) {
                                return Container(
                                  color: Colors.grey[800],
                                  child: const Center(
                                    child: Icon(
                                      Icons.image_not_supported,
                                      color: Colors.white54,
                                      size: 50,
                                    ),
                                  ),
                                );
                              },
                            ),
                          ),
                        ),
                        const SizedBox(height: 16),
                      ],
                    ),

                  // Title field
                  TextFormField(
                    controller: _titleController,
                    decoration: const InputDecoration(
                      labelText: 'Title',
                      border: OutlineInputBorder(),
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please enter a title';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),

                  // Image URL field
                  TextFormField(
                    controller: _imageUrlController,
                    autofocus: false, // Explicitly disable autofocus
                    decoration: InputDecoration(
                      labelText: 'Image URL',
                      border: const OutlineInputBorder(),
                      suffixIcon: IconButton(
                        icon: const Icon(Icons.refresh),
                        tooltip: 'Preview image',
                        onPressed: () {
                          // Force rebuild to show preview
                          setState(() {});
                        },
                      ),
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please enter an image URL';
                      }
                      if (!Uri.tryParse(value)!.isAbsolute) {
                        return 'Please enter a valid URL';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),

                  // Description field
                  TextFormField(
                    controller: _descriptionController,
                    autofocus: false, // Explicitly disable autofocus
                    decoration: const InputDecoration(
                      labelText: 'Description (optional)',
                      border: OutlineInputBorder(),
                    ),
                    maxLines: 3,
                  ),
                  const SizedBox(height: 16),

                  // Banner type selection
                  const Text(
                    'Banner Type:',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      Expanded(
                        child: RadioListTile<BannerType>(
                          title: const Text('Home'),
                          value: BannerType.home,
                          groupValue: _selectedType,
                          onChanged: (value) {
                            setState(() {
                              _selectedType = value!;
                            });
                          },
                        ),
                      ),
                      Expanded(
                        child: RadioListTile<BannerType>(
                          title: const Text('Splash'),
                          value: BannerType.splash,
                          groupValue: _selectedType,
                          onChanged: (value) {
                            setState(() {
                              _selectedType = value!;
                            });
                          },
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),

                  // Active status
                  SwitchListTile(
                    title: const Text(
                      'Active',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    subtitle: Text(
                      _isActive
                          ? 'Banner will be visible in the app'
                          : 'Banner will be hidden from the app',
                    ),
                    value: _isActive,
                    onChanged: (value) {
                      setState(() {
                        _isActive = value;
                      });
                    },
                  ),
                  const SizedBox(height: 24),

                  // Save button
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: isSubmitting ? null : _saveBanner,
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: isSubmitting
                          ? const CircularProgressIndicator()
                          : Text(
                              _isEditing ? 'Update Banner' : 'Add Banner',
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                    ),
                  ),
                ],
              ),
            ),
          );
        }),
      ),
    );
  }
}
