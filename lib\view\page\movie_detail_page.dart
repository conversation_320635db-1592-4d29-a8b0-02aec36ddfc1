import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:readmore/readmore.dart';

import '../../controllers/movie_controller.dart';
import '../../controllers/favorite_controller.dart';
import '../../controllers/auth_controller.dart';
import '../../models/movie_model.dart';
import '../../services/showtime_service.dart';
import '../../utils/app_colors.dart';
import '../user/movie_booking_page.dart';
import '../../widgets/trailer_player.dart';

class MovieDetailsPage extends StatefulWidget {
  final int? movieId;

  const MovieDetailsPage({Key? key, this.movieId}) : super(key: key);

  @override
  State<MovieDetailsPage> createState() => _MovieDetailsPageState();
}

class _MovieDetailsPageState extends State<MovieDetailsPage> {
  final MovieController _movieController = Get.find<MovieController>();
  final FavoriteController _favoriteController = Get.find<FavoriteController>();
  final AuthController _authController = Get.find<AuthController>();
  final ShowtimeService _showtimeService = ShowtimeService();

  final RxBool _expanded = false.obs;
  final RxDouble _velocity = 0.0.obs;
  final RxBool _hasAvailableShowtimes = false.obs;
  final RxBool _isCheckingShowtimes = false.obs;

  // Legacy movie details for backward compatibility
  final Map<String, dynamic> _legacyMovieDetails = {
    'title': 'Thor',
    'subtitle': 'The Dark World',
    'banner':
        'https://m.media-amazon.com/images/M/MV5BMTQyNzAwOTUxOF5BMl5BanBnXkFtZTcwMTE0OTc5OQ@@._V1_FMjpg_UX1000_.jpg',
    'genre': 'Action',
    'ar': '16+',
    'rating': '6.8',
    'plot':
        'When the Dark Elves attempt to plunge the universe into darkness, Thor must embark on a perilous and personal journey that will reunite him with doctor Jane Foster.',
    'casts': [
      {
        'name': 'Chris Hemsworth',
        'charecter': 'Thor',
        'image':
            'https://m.media-amazon.com/images/M/MV5BOTU2MTI0NTIyNV5BMl5BanBnXkFtZTcwMTA4Nzc3OA@@._V1_UX214_CR0,0,214,317_AL_.jpg',
      },
      {
        'name': 'Natalie Portman',
        'charecter': 'Jane Foster',
        'image':
            'https://m.media-amazon.com/images/M/MV5BOTU2MTI0NTIyNV5BMl5BanBnXkFtZTcwMTA4Nzc3OA@@._V1_UX214_CR0,0,214,317_AL_.jpg',
      },
      {
        'name': 'Tom Hiddleston',
        'charecter': 'Loki',
        'image':
            'https://m.media-amazon.com/images/M/MV5BOTU2MTI0NTIyNV5BMl5BanBnXkFtZTcwMTA4Nzc3OA@@._V1_UX214_CR0,0,214,317_AL_.jpg',
      },
    ]
  };

  @override
  void initState() {
    super.initState();
    if (widget.movieId != null) {
      _movieController.getMovieDetails(widget.movieId!);
      _checkMovieShowtimes(widget.movieId!);
    }
  }

  Future<void> _checkMovieShowtimes(int movieId) async {
    try {
      _isCheckingShowtimes.value = true;
      final showtimes = await _showtimeService.getShowtimesByMovie(movieId);

      // Check if there are any bookable showtimes (active, có ghế trống, và trong tương lai)
      final now = DateTime.now();
      final bookableShowtimes = showtimes.where((showtime) {
        return showtime.isBookable && showtime.showDateTime.isAfter(now);
      }).toList();

      _hasAvailableShowtimes.value = bookableShowtimes.isNotEmpty;

      print(
          'Movie $movieId: Found ${showtimes.length} total showtimes, ${bookableShowtimes.length} bookable');
    } catch (e) {
      print('Error checking showtimes for movie $movieId: $e');
      _hasAvailableShowtimes.value = false;
    } finally {
      _isCheckingShowtimes.value = false;
    }
  }

  void _onDragEnd(DragEndDetails details) {
    _velocity.value = details.velocity.pixelsPerSecond.dx.abs().floorToDouble();
    if (_velocity.value > 0) {
      _expanded.value = !_expanded.value;
    }
  }

  void _toggleFavorite(Movie movie) {
    _favoriteController.toggleFavorite(movie);
  }

  void _navigateToBooking(Movie movie) {
    if (!_authController.isLoggedIn) {
      Get.snackbar(
        'login_required'.tr,
        'please_login_to_book_tickets'.tr,
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.orange,
        colorText: Colors.white,
      );
      Get.toNamed('/login');
      return;
    }

    Get.to(() => MovieBookingPage(movie: movie));
  }

  Widget _buildBookingButton(Movie movie) {
    return Obx(() {
      // Check if we're still loading showtime data
      if (_isCheckingShowtimes.value) {
        return Container(
          width: double.infinity,
          height: 55,
          margin: const EdgeInsets.symmetric(vertical: 20),
          child: ElevatedButton(
            onPressed: null,
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.grey[600],
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(15),
              ),
              elevation: 5,
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                ),
                const SizedBox(width: 10),
                Text(
                  'checking'.tr,
                  style: GoogleFonts.mulish(
                    fontSize: 16,
                    fontWeight: FontWeight.w700,
                  ),
                ),
              ],
            ),
          ),
        );
      }

      // Check movie status and showtime availability
      final canBook = movie.isNowPlaying && _hasAvailableShowtimes.value;
      final isUpcoming = movie.isUpcoming;
      final isEnded = movie.isEnded;

      String buttonText;
      Color backgroundColor;
      Color foregroundColor;
      IconData iconData;
      VoidCallback? onPressed;

      if (isEnded) {
        buttonText = 'movie_ended'.tr;
        backgroundColor = Colors.grey[600]!;
        foregroundColor = Colors.white;
        iconData = Icons.event_busy;
        onPressed = null;
      } else if (isUpcoming) {
        buttonText = 'coming_soon'.tr;
        backgroundColor = Colors.blue[600]!;
        foregroundColor = Colors.white;
        iconData = Icons.schedule;
        onPressed = null;
      } else if (movie.isNowPlaying && !_hasAvailableShowtimes.value) {
        buttonText = 'no_showtimes_available'.tr;
        backgroundColor = Colors.orange[600]!;
        foregroundColor = Colors.white;
        iconData = Icons.event_note;
        onPressed = null;
      } else if (canBook) {
        buttonText = 'book_now'.tr;
        backgroundColor = Colors.amber;
        foregroundColor = Colors.black;
        iconData = Icons.movie_creation_outlined;
        onPressed = () => _navigateToBooking(movie);
      } else {
        buttonText = 'cannot_book_tickets'.tr;
        backgroundColor = Colors.grey[600]!;
        foregroundColor = Colors.white;
        iconData = Icons.block;
        onPressed = null;
      }

      return Container(
        width: double.infinity,
        height: 55,
        margin: const EdgeInsets.symmetric(vertical: 20),
        child: ElevatedButton(
          onPressed: onPressed,
          style: ElevatedButton.styleFrom(
            backgroundColor: backgroundColor,
            foregroundColor: foregroundColor,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(15),
            ),
            elevation: 5,
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                iconData,
                size: 24,
              ),
              const SizedBox(width: 10),
              Text(
                buttonText,
                style: GoogleFonts.mulish(
                  fontSize: 18,
                  fontWeight: FontWeight.w700,
                ),
              ),
            ],
          ),
        ),
      );
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Obx(() {
          // If we have a movie ID and the movie is loaded, show the movie details
          if (widget.movieId != null &&
              _movieController.selectedMovie.value != null) {
            final movie = _movieController.selectedMovie.value!;
            return _buildMovieDetails(context, movie);
          }

          // Otherwise, show the legacy movie details
          return _buildLegacyMovieDetails(context);
        }),
      ),
    );
  }

  Widget _buildMovieDetails(BuildContext context, Movie movie) {
    final isFavorite = _favoriteController.isFavorite(movie.id);

    return Container(
      height: MediaQuery.of(context).size.height,
      decoration: const BoxDecoration(
        gradient: AppColors.primaryGradientVertical,
      ),
      child: Stack(
        children: [
          // Movie backdrop
          SizedBox(
            height: MediaQuery.of(context).size.height * 0.6,
            width: double.infinity,
            child: Image.network(
              movie.fullBackdropPath,
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) {
                return Container(
                  color: Colors.grey[800],
                  child: const Center(
                    child: Icon(
                      Icons.image_not_supported,
                      color: Colors.white54,
                      size: 50,
                    ),
                  ),
                );
              },
            ),
          ),

          // Back button
          Positioned(
            top: MediaQuery.of(context).padding.top + 10,
            left: 10,
            child: IconButton(
              onPressed: () {
                Get.back();
              },
              icon: const Icon(
                Icons.arrow_back_ios_new,
                color: Colors.white,
              ),
              style: IconButton.styleFrom(
                backgroundColor: Colors.black38,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
              ),
            ),
          ),

          // Movie details card
          Obx(
            () => Positioned(
              top: _expanded.value
                  ? 0
                  : MediaQuery.of(context).size.height * 0.4,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                width: MediaQuery.of(context).size.width,
                height: _expanded.value
                    ? MediaQuery.of(context).size.height
                    : MediaQuery.of(context).size.height * .6,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.only(
                    topLeft: _expanded.value == true
                        ? Radius.zero
                        : const Radius.circular(50),
                    topRight: _expanded.value == true
                        ? Radius.zero
                        : const Radius.circular(50),
                  ),
                  gradient: AppColors.primaryGradientVertical,
                ),
                child: Column(
                  children: [
                    // Movie title section with drag handle
                    GestureDetector(
                      onVerticalDragEnd: _onDragEnd,
                      child: Column(
                        children: [
                          if (_expanded.value) const SizedBox(height: 25),
                          Text(
                            movie.title,
                            style: GoogleFonts.mulish(
                              fontSize: 40,
                              fontWeight: FontWeight.w700,
                              color: Colors.white,
                            ),
                            textAlign: TextAlign.center,
                          ),
                          if (movie.subtitle != null)
                            Text(
                              movie.subtitle!,
                              style: GoogleFonts.mulish(
                                fontSize: 18,
                                fontWeight: FontWeight.w700,
                                color: Colors.grey,
                              ),
                            ),
                          const SizedBox(height: 15),

                          // Movie info chips
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              // Genre, age rating, and IMDB rating
                              Expanded(
                                child: SingleChildScrollView(
                                  scrollDirection: Axis.horizontal,
                                  child: Row(
                                    children: [
                                      if (movie.genres.isNotEmpty)
                                        _buildInfoChip(
                                          movie.genres.first,
                                          Colors.blueGrey[300]!,
                                          Colors.white,
                                        ),
                                      const SizedBox(width: 8),
                                      if (movie.ageRating != null)
                                        _buildInfoChip(
                                          movie.ageRating!,
                                          Colors.red[400]!,
                                          Colors.white,
                                        ),
                                      const SizedBox(width: 8),
                                      _buildInfoChip(
                                        'IMDB ${movie.rating}',
                                        Colors.amber,
                                        Colors.black,
                                      ),
                                      const SizedBox(width: 8),
                                      if (movie.runtime != null)
                                        _buildInfoChip(
                                          '${movie.runtime}p',
                                          Colors.green[400]!,
                                          Colors.white,
                                        ),
                                    ],
                                  ),
                                ),
                              ),

                              // Share and favorite buttons
                              Row(
                                children: [
                                  _buildActionButton(
                                    Icons.share,
                                    Colors.blueGrey[100]!,
                                    () =>
                                        Get.toNamed('/share', arguments: movie),
                                  ),
                                  _buildActionButton(
                                    isFavorite
                                        ? Icons.favorite
                                        : Icons.favorite_border,
                                    isFavorite
                                        ? Colors.red
                                        : Colors.blueGrey[100]!,
                                    () => _toggleFavorite(movie),
                                  ),
                                ],
                              )
                            ],
                          ),
                          const SizedBox(height: 15),
                        ],
                      ),
                    ),

                    // Scrollable content (plot and cast)
                    Expanded(
                      child: SingleChildScrollView(
                        padding: const EdgeInsets.only(bottom: 10),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Plot
                            if (movie.overview != null)
                              ReadMoreText(
                                movie.overview!,
                                trimCollapsedText: 'More',
                                moreStyle: TextStyle(
                                  color: Colors.blue[300],
                                ),
                                lessStyle: TextStyle(
                                  color: Colors.red[200],
                                ),
                                trimExpandedText: 'Less',
                                textAlign: TextAlign.justify,
                                trimLines: 3,
                                trimMode: TrimMode.Line,
                                style: GoogleFonts.mulish(
                                  color: Colors.white,
                                  height: 1.5,
                                ),
                              ),
                            const SizedBox(height: 15),

                            // Trailer section
                            if (movie.trailerUrl != null &&
                                movie.trailerUrl!.isNotEmpty)
                              TrailerPlayer(
                                key: ValueKey(
                                    'trailer_${movie.id}_${movie.trailerUrl}'),
                                trailerUrl: movie.trailerUrl,
                                movieTitle: movie.title,
                              ),

                            // Thông tin chi tiết phim
                            _buildMovieInfoSection(movie),
                            const SizedBox(height: 20),

                            // Cast section
                            if (movie.cast != null &&
                                movie.cast!.isNotEmpty) ...[
                              _buildCastSection(movie.cast!),
                              const SizedBox(height: 30),
                            ],

                            // Book Ticket Button
                            _buildBookingButton(movie),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Legacy implementation for backward compatibility
  Widget _buildLegacyMovieDetails(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height,
      decoration: const BoxDecoration(
        gradient: AppColors.primaryGradientVertical,
      ),
      child: Stack(
        children: [
          SizedBox(
            height: MediaQuery.of(context).size.height * 0.6,
            width: double.infinity,
            child: Image.network(
              _legacyMovieDetails['banner'].toString(),
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) {
                return Container(
                  color: Colors.grey[800],
                  child: const Center(
                    child: Icon(
                      Icons.image_not_supported,
                      color: Colors.white54,
                      size: 50,
                    ),
                  ),
                );
              },
            ),
          ),

          // Back button
          Positioned(
            top: MediaQuery.of(context).padding.top + 10,
            left: 10,
            child: IconButton(
              onPressed: () {
                Get.back();
              },
              icon: const Icon(
                Icons.arrow_back_ios_new,
                color: Colors.white,
              ),
              style: IconButton.styleFrom(
                backgroundColor: Colors.black38,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
              ),
            ),
          ),

          Obx(
            () => Positioned(
              top: _expanded.value
                  ? 0
                  : MediaQuery.of(context).size.height * 0.4,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                width: MediaQuery.of(context).size.width,
                height: _expanded.value
                    ? MediaQuery.of(context).size.height
                    : MediaQuery.of(context).size.height * .6,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.only(
                    topLeft: _expanded.value == true
                        ? Radius.zero
                        : const Radius.circular(50),
                    topRight: _expanded.value == true
                        ? Radius.zero
                        : const Radius.circular(50),
                  ),
                  gradient: AppColors.primaryGradientVertical,
                ),
                child: Column(
                  children: [
                    GestureDetector(
                      onVerticalDragEnd: _onDragEnd,
                      child: Column(
                        children: [
                          if (_expanded.value) const SizedBox(height: 25),
                          Text(
                            _legacyMovieDetails['title'].toString(),
                            style: GoogleFonts.mulish(
                              fontSize: 40,
                              fontWeight: FontWeight.w700,
                              color: Colors.white,
                            ),
                            textAlign: TextAlign.center,
                          ),
                          Text(
                            _legacyMovieDetails['subtitle'].toString(),
                            style: GoogleFonts.mulish(
                              fontSize: 18,
                              fontWeight: FontWeight.w700,
                              color: Colors.grey,
                            ),
                          ),
                          const SizedBox(height: 15),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Row(
                                children: [
                                  Chip(
                                    backgroundColor: Colors.blueGrey[300],
                                    label: Text(
                                      _legacyMovieDetails['genre'].toString(),
                                      style: GoogleFonts.mulish(
                                        fontSize: 12,
                                        color: Colors.white,
                                        fontWeight: FontWeight.w700,
                                      ),
                                    ),
                                  ),
                                  const SizedBox(width: 5),
                                  Chip(
                                    backgroundColor: Colors.blueGrey[300],
                                    label: Text(
                                      _legacyMovieDetails['ar'].toString(),
                                      style: GoogleFonts.mulish(
                                        fontSize: 12,
                                        color: Colors.white,
                                        fontWeight: FontWeight.w700,
                                      ),
                                    ),
                                  ),
                                  const SizedBox(width: 5),
                                  Chip(
                                    backgroundColor: Colors.amber,
                                    label: Text(
                                      'IMDB ${_legacyMovieDetails['rating']}',
                                      style: GoogleFonts.mulish(
                                        fontSize: 12,
                                        fontWeight: FontWeight.w700,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                              Row(
                                children: [
                                  IconButton(
                                    onPressed: () {},
                                    icon: Icon(
                                      Icons.share,
                                      color: Colors.blueGrey[100],
                                    ),
                                  ),
                                  IconButton(
                                    onPressed: () {},
                                    icon: Icon(
                                      Icons.favorite_border,
                                      color: Colors.blueGrey[100],
                                    ),
                                  ),
                                ],
                              )
                            ],
                          ),
                          const SizedBox(height: 15),
                        ],
                      ),
                    ),
                    Expanded(
                      child: SingleChildScrollView(
                        padding: const EdgeInsets.only(bottom: 10),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            ReadMoreText(
                              _legacyMovieDetails['plot'].toString(),
                              trimCollapsedText: 'More',
                              moreStyle: TextStyle(
                                color: Colors.blue[300],
                              ),
                              lessStyle: TextStyle(
                                color: Colors.red[200],
                              ),
                              trimExpandedText: 'Less',
                              textAlign: TextAlign.justify,
                              trimLines: 3,
                              trimMode: TrimMode.Line,
                              style: GoogleFonts.mulish(
                                color: Colors.white,
                                height: 1.5,
                              ),
                            ),
                            const SizedBox(height: 15),

                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                  'Diễn viên',
                                  style: GoogleFonts.mulish(
                                    color: Colors.white,
                                    fontSize: 18,
                                    fontWeight: FontWeight.w700,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 15),
                            SizedBox(
                              height: 150,
                              child: ListView.builder(
                                scrollDirection: Axis.horizontal,
                                itemCount:
                                    (_legacyMovieDetails['casts'] as List)
                                        .length,
                                itemBuilder: (context, index) {
                                  final cast = (_legacyMovieDetails['casts']
                                      as List)[index];
                                  return Container(
                                    width: 80,
                                    margin: const EdgeInsets.only(right: 10),
                                    child: Column(
                                      children: [
                                        ClipRRect(
                                          borderRadius:
                                              BorderRadius.circular(15),
                                          child: Image.network(
                                            cast['image'],
                                            width: 70,
                                            height: 70,
                                            fit: BoxFit.cover,
                                          ),
                                        ),
                                        const SizedBox(height: 5),
                                        Text(
                                          cast['name'],
                                          textAlign: TextAlign.center,
                                          style: GoogleFonts.mulish(
                                            fontSize: 12,
                                            fontWeight: FontWeight.w500,
                                            color: Colors.white,
                                          ),
                                          maxLines: 2,
                                          overflow: TextOverflow.ellipsis,
                                        ),
                                        const SizedBox(height: 5),
                                        Text(
                                          cast['charecter'],
                                          textAlign: TextAlign.center,
                                          style: GoogleFonts.mulish(
                                            fontSize: 10,
                                            fontWeight: FontWeight.w500,
                                            color: Colors.white54,
                                          ),
                                          maxLines: 2,
                                          overflow: TextOverflow.ellipsis,
                                        ),
                                      ],
                                    ),
                                  );
                                },
                              ),
                            ),
                            const SizedBox(height: 30),

                            // Book Ticket Button for Legacy Movie
                            Container(
                              width: double.infinity,
                              height: 55,
                              margin: const EdgeInsets.symmetric(vertical: 20),
                              child: ElevatedButton(
                                onPressed: () {
                                  Get.snackbar(
                                    'Thông báo',
                                    'Chức năng đặt vé chỉ khả dụng cho phim từ cơ sở dữ liệu',
                                    snackPosition: SnackPosition.BOTTOM,
                                    backgroundColor: Colors.orange,
                                    colorText: Colors.white,
                                  );
                                },
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: Colors.grey[600],
                                  foregroundColor: Colors.white,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(15),
                                  ),
                                  elevation: 5,
                                ),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    const Icon(
                                      Icons.movie_creation_outlined,
                                      size: 24,
                                    ),
                                    const SizedBox(width: 10),
                                    Text(
                                      'Đặt vé (Không khả dụng)',
                                      style: GoogleFonts.mulish(
                                        fontSize: 16,
                                        fontWeight: FontWeight.w700,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Helper method để tạo info chip đẹp hơn
  Widget _buildInfoChip(String text, Color backgroundColor, Color textColor) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: backgroundColor.withOpacity(0.3),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Text(
        text,
        style: GoogleFonts.mulish(
          fontSize: 12,
          color: textColor,
          fontWeight: FontWeight.w700,
        ),
      ),
    );
  }

  // Helper method để tạo action button đẹp hơn
  Widget _buildActionButton(
      IconData icon, Color color, VoidCallback onPressed) {
    return Container(
      margin: const EdgeInsets.only(left: 8),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.white.withOpacity(0.2)),
      ),
      child: IconButton(
        onPressed: onPressed,
        icon: Icon(icon, color: color, size: 22),
        padding: const EdgeInsets.all(8),
        constraints: const BoxConstraints(minWidth: 40, minHeight: 40),
      ),
    );
  }

  // Widget hiển thị thông tin chi tiết phim
  Widget _buildMovieInfoSection(Movie movie) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.05),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.white.withOpacity(0.1)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Thông tin phim',
            style: GoogleFonts.mulish(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.w700,
            ),
          ),
          const SizedBox(height: 12),
          if (movie.director != null) ...[
            _buildInfoRow('Đạo diễn', movie.director!),
            const SizedBox(height: 8),
          ],
          if (movie.releaseDate != null) ...[
            _buildInfoRow('Ngày phát hành', movie.releaseDate!),
            const SizedBox(height: 8),
          ],
          if (movie.country != null) ...[
            _buildInfoRow('Quốc gia', movie.country!),
            const SizedBox(height: 8),
          ],
          if (movie.language != null) ...[
            _buildInfoRow('Ngôn ngữ', movie.language!),
            const SizedBox(height: 8),
          ],
          if (movie.genres.isNotEmpty) ...[
            _buildInfoRow('Thể loại', movie.genres.join(', ')),
          ],
        ],
      ),
    );
  }

  // Widget hiển thị một dòng thông tin
  Widget _buildInfoRow(String label, String value) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 100,
          child: Text(
            label,
            style: GoogleFonts.mulish(
              color: Colors.white70,
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        const Text(
          ': ',
          style: TextStyle(color: Colors.white70),
        ),
        Expanded(
          child: Text(
            value,
            style: GoogleFonts.mulish(
              color: Colors.white,
              fontSize: 14,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ],
    );
  }

  // Widget hiển thị danh sách diễn viên được cải thiện
  Widget _buildCastSection(List<Cast> cast) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Diễn viên',
              style: GoogleFonts.mulish(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.w700,
              ),
            ),
            if (cast.length > 4)
              TextButton(
                onPressed: () {
                  _showFullCastDialog(cast);
                },
                child: Text(
                  'Xem tất cả',
                  style: GoogleFonts.mulish(
                    color: Colors.amber,
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
          ],
        ),
        const SizedBox(height: 15),
        SizedBox(
          height: 180,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: cast.length > 6 ? 6 : cast.length,
            itemBuilder: (context, index) {
              final castMember = cast[index];
              return _buildCastCard(castMember);
            },
          ),
        ),
      ],
    );
  }

  // Widget card diễn viên được cải thiện
  Widget _buildCastCard(Cast cast) {
    return GestureDetector(
      onTap: () => _showCastDetail(cast),
      child: Container(
        width: 110,
        margin: const EdgeInsets.only(right: 12),
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.05),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(color: Colors.white.withOpacity(0.1)),
        ),
        child: Column(
          children: [
            const SizedBox(height: 8),
            ClipRRect(
              borderRadius: BorderRadius.circular(50),
              child: Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.3),
                      blurRadius: 8,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: Image.network(
                  cast.fullProfilePath,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    return Container(
                      color: Colors.grey[800],
                      child: const Icon(
                        Icons.person,
                        color: Colors.white54,
                        size: 40,
                      ),
                    );
                  },
                ),
              ),
            ),
            const SizedBox(height: 12),
            Expanded(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 8),
                child: Column(
                  children: [
                    Text(
                      cast.name,
                      textAlign: TextAlign.center,
                      style: GoogleFonts.mulish(
                        fontSize: 13,
                        fontWeight: FontWeight.w600,
                        color: Colors.white,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      cast.character,
                      textAlign: TextAlign.center,
                      style: GoogleFonts.mulish(
                        fontSize: 11,
                        fontWeight: FontWeight.w500,
                        color: Colors.white60,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 8),
          ],
        ),
      ),
    );
  }

  // Hiển thị dialog chi tiết diễn viên
  void _showCastDetail(Cast cast) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        backgroundColor: Colors.transparent,
        child: Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            gradient: AppColors.primaryGradientVertical,
            borderRadius: BorderRadius.circular(20),
            border: Border.all(color: Colors.white.withOpacity(0.2)),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ClipRRect(
                borderRadius: BorderRadius.circular(60),
                child: Image.network(
                  cast.fullProfilePath,
                  width: 120,
                  height: 120,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    return Container(
                      width: 120,
                      height: 120,
                      color: Colors.grey[800],
                      child: const Icon(
                        Icons.person,
                        color: Colors.white54,
                        size: 60,
                      ),
                    );
                  },
                ),
              ),
              const SizedBox(height: 16),
              Text(
                cast.name,
                style: GoogleFonts.mulish(
                  fontSize: 20,
                  fontWeight: FontWeight.w700,
                  color: Colors.white,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8),
              Text(
                'vai diễn: ${cast.character}',
                style: GoogleFonts.mulish(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: Colors.white70,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 20),
              ElevatedButton(
                onPressed: () => Get.back(),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.amber,
                  foregroundColor: Colors.black,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: Text(
                  'Đóng',
                  style: GoogleFonts.mulish(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Hiển thị dialog danh sách đầy đủ diễn viên
  void _showFullCastDialog(List<Cast> cast) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        backgroundColor: Colors.transparent,
        child: Container(
          height: MediaQuery.of(context).size.height * 0.8,
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            gradient: AppColors.primaryGradientVertical,
            borderRadius: BorderRadius.circular(20),
            border: Border.all(color: Colors.white.withOpacity(0.2)),
          ),
          child: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Toàn bộ diễn viên',
                    style: GoogleFonts.mulish(
                      fontSize: 20,
                      fontWeight: FontWeight.w700,
                      color: Colors.white,
                    ),
                  ),
                  IconButton(
                    onPressed: () => Get.back(),
                    icon: const Icon(
                      Icons.close,
                      color: Colors.white,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Expanded(
                child: GridView.builder(
                  gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 2,
                    childAspectRatio: 0.8,
                    crossAxisSpacing: 12,
                    mainAxisSpacing: 12,
                  ),
                  itemCount: cast.length,
                  itemBuilder: (context, index) {
                    final castMember = cast[index];
                    return _buildCastCard(castMember);
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
