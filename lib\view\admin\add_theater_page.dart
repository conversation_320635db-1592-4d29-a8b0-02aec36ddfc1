import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../models/theater_model.dart';
import '../../services/theater_service.dart';

class AddTheaterPage extends StatefulWidget {
  const AddTheaterPage({Key? key}) : super(key: key);

  @override
  State<AddTheaterPage> createState() => _AddTheaterPageState();
}

class _AddTheaterPageState extends State<AddTheaterPage> {
  final TheaterService _theaterService = TheaterService();
  final _formKey = GlobalKey<FormState>();
  final RxBool _isLoading = false.obs;

  // Form controllers
  final _nameController = TextEditingController();
  final _streetController = TextEditingController();
  final _districtController = TextEditingController();
  final _cityController = TextEditingController();
  final _provinceController = TextEditingController();
  final _phoneController = TextEditingController();
  final _emailController = TextEditingController();
  final _latitudeController = TextEditingController();
  final _longitudeController = TextEditingController();

  // Facilities
  final RxList<String> _selectedFacilities = <String>[].obs;
  final List<String> _availableFacilities = [
    'parking',
    'food_court',
    '3d',
    'imax',
    'vip',
    'air_conditioning',
    'elevator',
    'wheelchair_access',
  ];

  // Operating hours
  final Map<String, Map<String, TextEditingController>> _operatingHours = {
    'monday': {
      'open': TextEditingController(text: '08:00'),
      'close': TextEditingController(text: '23:00')
    },
    'tuesday': {
      'open': TextEditingController(text: '08:00'),
      'close': TextEditingController(text: '23:00')
    },
    'wednesday': {
      'open': TextEditingController(text: '08:00'),
      'close': TextEditingController(text: '23:00')
    },
    'thursday': {
      'open': TextEditingController(text: '08:00'),
      'close': TextEditingController(text: '23:00')
    },
    'friday': {
      'open': TextEditingController(text: '08:00'),
      'close': TextEditingController(text: '23:00')
    },
    'saturday': {
      'open': TextEditingController(text: '08:00'),
      'close': TextEditingController(text: '23:00')
    },
    'sunday': {
      'open': TextEditingController(text: '08:00'),
      'close': TextEditingController(text: '23:00')
    },
  };

  Future<void> _saveTheater() async {
    if (!_formKey.currentState!.validate()) return;

    try {
      _isLoading.value = true;

      // Create operating hours map
      final operatingHoursMap = <String, TheaterOperatingHours>{};
      _operatingHours.forEach((day, controllers) {
        operatingHoursMap[day] = TheaterOperatingHours(
          open: controllers['open']!.text,
          close: controllers['close']!.text,
        );
      });

      // Create theater
      final theater = TheaterModel(
        id: '', // Will be set by Firestore
        name: _nameController.text.trim(),
        address: TheaterAddress(
          street: _streetController.text.trim(),
          district: _districtController.text.trim(),
          city: _cityController.text.trim(),
          province: _provinceController.text.trim(),
          coordinates: TheaterCoordinates(
            latitude: double.tryParse(_latitudeController.text) ?? 0.0,
            longitude: double.tryParse(_longitudeController.text) ?? 0.0,
          ),
        ),
        phoneNumber: _phoneController.text.trim(),
        email: _emailController.text.trim().isEmpty
            ? null
            : _emailController.text.trim(),
        facilities: _selectedFacilities.toList(),
        operatingHours: operatingHoursMap,
        isActive: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      await _theaterService.addTheater(theater);

      Get.back();
      Get.snackbar(
        'Thành công',
        'Đã thêm rạp mới thành công',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green.withOpacity(0.7),
        colorText: Colors.white,
      );
    } catch (e) {
      Get.snackbar(
        'Lỗi',
        'Không thể thêm rạp: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.withOpacity(0.7),
        colorText: Colors.white,
      );
    } finally {
      _isLoading.value = false;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        height: MediaQuery.of(context).size.height,
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            colors: [
              Color(0xff2B5876),
              Color(0xff4E4376),
            ],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              // App Bar
              Container(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    IconButton(
                      onPressed: () => Get.back(),
                      icon: const Icon(Icons.arrow_back, color: Colors.white),
                    ),
                    Expanded(
                      child: Text(
                        'Thêm Rạp Mới',
                        style: GoogleFonts.mulish(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                    ),
                    Obx(() => TextButton(
                          onPressed: _isLoading.value ? null : _saveTheater,
                          child: _isLoading.value
                              ? const SizedBox(
                                  width: 20,
                                  height: 20,
                                  child: CircularProgressIndicator(
                                    color: Colors.white,
                                    strokeWidth: 2,
                                  ),
                                )
                              : Text(
                                  'Lưu',
                                  style: GoogleFonts.mulish(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.white,
                                  ),
                                ),
                        )),
                  ],
                ),
              ),

              // Form
              Expanded(
                child: Form(
                  key: _formKey,
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Basic Info
                        _buildSectionTitle('Thông tin cơ bản'),
                        _buildTextField(
                          controller: _nameController,
                          label: 'Tên rạp',
                          hint: 'Nhập tên rạp chiếu phim',
                          validator: (value) {
                            if (value == null || value.trim().isEmpty) {
                              return 'Vui lòng nhập tên rạp';
                            }
                            return null;
                          },
                        ),
                        const SizedBox(height: 12),
                        _buildTextField(
                          controller: _phoneController,
                          label: 'Số điện thoại',
                          hint: 'Nhập số điện thoại',
                          keyboardType: TextInputType.phone,
                          validator: (value) {
                            if (value == null || value.trim().isEmpty) {
                              return 'Vui lòng nhập số điện thoại';
                            }
                            return null;
                          },
                        ),
                        const SizedBox(height: 12),
                        _buildTextField(
                          controller: _emailController,
                          label: 'Email (tùy chọn)',
                          hint: 'Nhập email',
                          keyboardType: TextInputType.emailAddress,
                        ),

                        const SizedBox(height: 24),

                        // Address
                        _buildSectionTitle('Địa chỉ'),
                        _buildTextField(
                          controller: _streetController,
                          label: 'Đường/Số nhà',
                          hint: 'Nhập địa chỉ cụ thể',
                          validator: (value) {
                            if (value == null || value.trim().isEmpty) {
                              return 'Vui lòng nhập địa chỉ';
                            }
                            return null;
                          },
                        ),
                        const SizedBox(height: 12),
                        Row(
                          children: [
                            Expanded(
                              child: _buildTextField(
                                controller: _districtController,
                                label: 'Quận/Huyện',
                                hint: 'Nhập quận/huyện',
                                validator: (value) {
                                  if (value == null || value.trim().isEmpty) {
                                    return 'Vui lòng nhập quận/huyện';
                                  }
                                  return null;
                                },
                              ),
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: _buildTextField(
                                controller: _cityController,
                                label: 'Thành phố',
                                hint: 'Nhập thành phố',
                                validator: (value) {
                                  if (value == null || value.trim().isEmpty) {
                                    return 'Vui lòng nhập thành phố';
                                  }
                                  return null;
                                },
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 12),
                        _buildTextField(
                          controller: _provinceController,
                          label: 'Tỉnh/Thành phố',
                          hint: 'Nhập tỉnh/thành phố',
                          validator: (value) {
                            if (value == null || value.trim().isEmpty) {
                              return 'Vui lòng nhập tỉnh/thành phố';
                            }
                            return null;
                          },
                        ),

                        const SizedBox(height: 24),

                        // Coordinates
                        _buildSectionTitle('Tọa độ (tùy chọn)'),
                        Row(
                          children: [
                            Expanded(
                              child: _buildTextField(
                                controller: _latitudeController,
                                label: 'Vĩ độ',
                                hint: 'Nhập vĩ độ',
                                keyboardType: TextInputType.number,
                              ),
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: _buildTextField(
                                controller: _longitudeController,
                                label: 'Kinh độ',
                                hint: 'Nhập kinh độ',
                                keyboardType: TextInputType.number,
                              ),
                            ),
                          ],
                        ),

                        const SizedBox(height: 24),

                        // Facilities
                        _buildSectionTitle('Tiện ích'),
                        _buildFacilitiesSection(),

                        const SizedBox(height: 24),

                        // Operating Hours
                        _buildSectionTitle('Giờ hoạt động'),
                        _buildOperatingHoursSection(),

                        const SizedBox(height: 32),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Text(
        title,
        style: GoogleFonts.mulish(
          fontSize: 16,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    TextInputType? keyboardType,
    String? Function(String?)? validator,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.white.withOpacity(0.2)),
      ),
      child: TextFormField(
        controller: controller,
        keyboardType: keyboardType,
        validator: validator,
        autofocus: false, // Explicitly disable autofocus
        style: const TextStyle(color: Colors.white),
        decoration: InputDecoration(
          labelText: label,
          hintText: hint,
          labelStyle: TextStyle(color: Colors.white.withOpacity(0.8)),
          hintStyle: TextStyle(color: Colors.white.withOpacity(0.5)),
          border: InputBorder.none,
          contentPadding: const EdgeInsets.all(16),
        ),
      ),
    );
  }

  Widget _buildFacilitiesSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.white.withOpacity(0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Chọn tiện ích có sẵn:',
            style: GoogleFonts.mulish(
              fontSize: 14,
              color: Colors.white.withOpacity(0.8),
            ),
          ),
          const SizedBox(height: 12),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: _availableFacilities
                .map(
                  (facility) => Obx(() => FilterChip(
                        label: Text(
                          _getFacilityDisplayName(facility),
                          style: GoogleFonts.mulish(
                            fontSize: 12,
                            color: _selectedFacilities.contains(facility)
                                ? Colors.white
                                : Colors.white.withOpacity(0.7),
                          ),
                        ),
                        selected: _selectedFacilities.contains(facility),
                        onSelected: (selected) {
                          if (selected) {
                            _selectedFacilities.add(facility);
                          } else {
                            _selectedFacilities.remove(facility);
                          }
                        },
                        backgroundColor: Colors.white.withOpacity(0.1),
                        selectedColor: Colors.blue.withOpacity(0.3),
                        checkmarkColor: Colors.white,
                        side: BorderSide(
                          color: _selectedFacilities.contains(facility)
                              ? Colors.blue
                              : Colors.white.withOpacity(0.3),
                        ),
                      )),
                )
                .toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildOperatingHoursSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.white.withOpacity(0.2)),
      ),
      child: Column(
        children: _operatingHours.entries
            .map(
              (entry) => Padding(
                padding: const EdgeInsets.symmetric(vertical: 4),
                child: Row(
                  children: [
                    SizedBox(
                      width: 80,
                      child: Text(
                        _getDayName(entry.key),
                        style: GoogleFonts.mulish(
                          fontSize: 14,
                          color: Colors.white.withOpacity(0.8),
                        ),
                      ),
                    ),
                    Expanded(
                      child: Row(
                        children: [
                          Expanded(
                            child: Container(
                              height: 40,
                              decoration: BoxDecoration(
                                color: Colors.white.withOpacity(0.05),
                                borderRadius: BorderRadius.circular(8),
                                border: Border.all(
                                    color: Colors.white.withOpacity(0.1)),
                              ),
                              child: TextFormField(
                                controller: entry.value['open']!,
                                autofocus:
                                    false, // Explicitly disable autofocus
                                style: const TextStyle(
                                    color: Colors.white, fontSize: 12),
                                textAlign: TextAlign.center,
                                decoration: const InputDecoration(
                                  border: InputBorder.none,
                                  contentPadding:
                                      EdgeInsets.symmetric(horizontal: 8),
                                ),
                              ),
                            ),
                          ),
                          Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 8),
                            child: Text(
                              '-',
                              style: GoogleFonts.mulish(
                                fontSize: 14,
                                color: Colors.white.withOpacity(0.8),
                              ),
                            ),
                          ),
                          Expanded(
                            child: Container(
                              height: 40,
                              decoration: BoxDecoration(
                                color: Colors.white.withOpacity(0.05),
                                borderRadius: BorderRadius.circular(8),
                                border: Border.all(
                                    color: Colors.white.withOpacity(0.1)),
                              ),
                              child: TextFormField(
                                controller: entry.value['close']!,
                                autofocus:
                                    false, // Explicitly disable autofocus
                                style: const TextStyle(
                                    color: Colors.white, fontSize: 12),
                                textAlign: TextAlign.center,
                                decoration: const InputDecoration(
                                  border: InputBorder.none,
                                  contentPadding:
                                      EdgeInsets.symmetric(horizontal: 8),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            )
            .toList(),
      ),
    );
  }

  String _getFacilityDisplayName(String facility) {
    switch (facility) {
      case 'parking':
        return 'Bãi đỗ xe';
      case 'food_court':
        return 'Khu ăn uống';
      case '3d':
        return '3D';
      case 'imax':
        return 'IMAX';
      case 'vip':
        return 'VIP';
      case 'air_conditioning':
        return 'Điều hòa';
      case 'elevator':
        return 'Thang máy';
      case 'wheelchair_access':
        return 'Tiếp cận xe lăn';
      default:
        return facility;
    }
  }

  String _getDayName(String day) {
    switch (day.toLowerCase()) {
      case 'monday':
        return 'Thứ 2';
      case 'tuesday':
        return 'Thứ 3';
      case 'wednesday':
        return 'Thứ 4';
      case 'thursday':
        return 'Thứ 5';
      case 'friday':
        return 'Thứ 6';
      case 'saturday':
        return 'Thứ 7';
      case 'sunday':
        return 'CN';
      default:
        return day;
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _streetController.dispose();
    _districtController.dispose();
    _cityController.dispose();
    _provinceController.dispose();
    _phoneController.dispose();
    _emailController.dispose();
    _latitudeController.dispose();
    _longitudeController.dispose();

    _operatingHours.forEach((_, controllers) {
      controllers.forEach((_, controller) {
        controller.dispose();
      });
    });

    super.dispose();
  }
}
