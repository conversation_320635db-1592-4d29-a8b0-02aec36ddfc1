/// <PERSON><PERSON><PERSON><PERSON> khởi đầu ch<PERSON>h cho ứng dụng đặt vé xem phim Đớp Phim
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:media_kit/media_kit.dart';
import 'firebase_options.dart';
import 'services/firebase_config.dart';
import 'controllers/auth_controller.dart';
import 'controllers/language_controller.dart';
import 'controllers/movie_controller.dart';
import 'controllers/favorite_controller.dart';
import 'controllers/ticket_controller.dart';
import 'controllers/booking_controller.dart';
import 'controllers/schedule_controller.dart';
import 'controllers/banner_controller.dart';
import 'controllers/realtime_notification_controller.dart';
import 'controllers/realtime_bug_report_controller.dart';
import 'bindings/realtime_database_binding.dart';
import 'services/reservation_cleanup_service.dart';
import 'services/ticket_expiration_service.dart';
import 'services/showtime_status_service.dart';
import 'utils/developer_mode.dart';
import 'utils/app_colors.dart';
import 'translations/app_translations.dart';
import 'view/page/auth/login_page.dart';
import 'view/page/auth/email_verification_page.dart';
import 'view/page/splash_screen.dart';
import 'view/page/profile_edit_page.dart';
import 'view/page/change_password_page.dart';
import 'view/page/bug_report_detail_page.dart';
import 'view/page/notification_page.dart';
import 'view/page/notification_settings_page.dart';
import 'view/page/realtime_notification_tabs_page.dart';
import 'view/page/realtime_bug_report_detail_page.dart';
import 'view/page/search_page.dart';
import 'view/page/all_movies_page.dart';
import 'view/root_page.dart';
import 'view/admin/admin_routes.dart';
import 'debug_firestore.dart';
import 'debug_add_banner.dart';

/// Khởi tạo và thiết lập ứng dụng
void main() async {
  // Đảm bảo Flutter binding được khởi tạo trước các thao tác bất đồng bộ
  WidgetsFlutterBinding.ensureInitialized();

  // Khởi tạo MediaKit cho phát video (trailer)
  MediaKit.ensureInitialized();

  // Khóa ứng dụng chỉ ở chế độ dọc
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);

  // Khởi tạo Firebase với các tùy chọn theo nền tảng
  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );

  // Configure Firebase settings to reduce console warnings
  // Cấu hình Firebase để giảm cảnh báo console
  FirebaseConfig.configureFirebaseLocale();

  // Khởi tạo Firebase App Check cho bảo mật (tùy chọn)
  await FirebaseConfig.initializeAppCheck();

  // Cấu hình thiết lập Firebase Authentication
  await FirebaseConfig.configureFirebaseAuth();

  // Khởi tạo tất cả GetX controllers để quản lý trạng thái
  Get.put(AuthController());
  Get.put(LanguageController());
  Get.put(MovieController());
  Get.put(FavoriteController());
  Get.put(TicketController());
  Get.put(BookingController());
  Get.put(ScheduleController());
  Get.put(BannerController());
  Get.put(RealtimeNotificationController());
  Get.put(RealtimeBugReportController());
  Get.put(DeveloperMode());

  // Khởi tạo các dịch vụ chạy nền
  Get.put(ReservationCleanupService()); // Dọn dẹp đặt chỗ hết hạn
  Get.put(TicketExpirationService()); // Xử lý vé hết hạn

  // Khởi tạo và bắt đầu service cập nhật trạng thái showtime
  final showtimeStatusService = ShowtimeStatusService();
  showtimeStatusService.startAutoStatusUpdate(
    interval: const Duration(minutes: 5), // Cập nhật mỗi 5 phút
  );
  Get.put(showtimeStatusService);

  // Khởi tạo database bindings cho tính năng realtime
  RealtimeDatabaseBinding().dependencies();

  runApp(const MyApp());
}

/// Widget chính của ứng dụng với cấu hình theme và routing
class MyApp extends StatelessWidget {
  const MyApp({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final languageController = Get.find<LanguageController>();

    return GetMaterialApp(
      title: 'Đớp Phim',
      debugShowCheckedModeBanner: false,

      // Cấu hình hỗ trợ đa ngôn ngữ
      translations: AppTranslations(),
      locale: languageController.currentLocale,
      fallbackLocale: const Locale('en', 'US'),

      // Cấu hình định tuyến ứng dụng
      getPages: [
        // Các route admin
        ...AdminRoutes.routes,

        // Các route debug cho phát triển
        GetPage(
          name: '/debug',
          page: () => const FirestoreDebugPage(),
        ),
        GetPage(
          name: '/debug/add-banner',
          page: () => const AddBannerDebugPage(),
        ),

        // Các route hồ sơ người dùng
        GetPage(
          name: '/profile/edit',
          page: () => const ProfileEditPage(),
        ),
        GetPage(
          name: '/change_password',
          page: () => const ChangePasswordPage(),
        ),
        GetPage(
          name: '/email_verification',
          page: () => const EmailVerificationPage(),
        ),

        // Các route tìm kiếm và duyệt phim
        GetPage(
          name: '/search',
          page: () => SearchPage(
            initialQuery: Get.parameters['query'],
          ),
        ),
        GetPage(
          name: '/all_movies',
          page: () => AllMoviesPage(
            genre: Get.parameters['genre'],
          ),
        ),

        // Bug report routes with realtime support
        // Các route báo lỗi với hỗ trợ realtime
        GetPage(
          name: '/bug_report_detail',
          page: () {
            // Check if bugReportId parameter exists
            // Kiểm tra xem có tham số bugReportId không
            final bugReportId = Get.parameters['bugReportId'];

            // If has 'rt_' prefix, use realtime bug report page
            // Nếu có tiền tố 'rt_', sử dụng trang báo lỗi realtime
            if (bugReportId != null && bugReportId.startsWith('rt_')) {
              final realBugReportId = bugReportId
                  .substring(3); // Remove 'rt_' prefix / Bỏ tiền tố 'rt_'
              return RealtimeBugReportDetailPage(bugReportId: realBugReportId);
            }

            // Otherwise, use regular bug report page
            // Ngược lại, sử dụng trang báo lỗi thông thường
            return const BugReportDetailPage();
          },
          binding: RealtimeDatabaseBinding(),
        ),

        // Các route thông báo
        GetPage(
          name: '/notifications',
          page: () => const NotificationPage(
              showBackButton:
                  true), // Hiển thị nút back khi navigate từ màn khác
          binding: RealtimeDatabaseBinding(),
        ),
        GetPage(
          name: '/realtime_notifications',
          page: () => const RealtimeNotificationTabsPage(),
          binding: RealtimeDatabaseBinding(),
        ),
        GetPage(
          name: '/realtime_bug_report_detail/:bugReportId',
          page: () => RealtimeBugReportDetailPage(
            bugReportId: Get.parameters['bugReportId'] ?? '',
          ),
          binding: RealtimeDatabaseBinding(),
        ),
        GetPage(
          name: '/notification_settings',
          page: () => const NotificationSettingsPage(),
          binding: RealtimeDatabaseBinding(),
        ),
      ],
      initialRoute: '/',

      // App theme configuration - Cinema Modern theme
      // Cấu hình theme ứng dụng với Cinema Modern theme (đỏ burgundy + vàng gold)
      theme: ThemeData(
        primarySwatch: Colors.red,
        brightness: Brightness.dark,
        scaffoldBackgroundColor: AppColors.scaffoldBackground,
        cardColor: AppColors.cardBackground,
        primaryColor: AppColors.primaryRed,

        // Color scheme for consistent theming across components
        // Bảng màu Cinema Modern để đảm bảo theme nhất quán trên các component
        colorScheme: const ColorScheme.dark(
          primary: AppColors.primaryRed,
          secondary: AppColors.primaryGold,
          surface: AppColors.surfaceColor,
          error: AppColors.errorRed,
          onPrimary: AppColors.textOnDark,
          onSecondary: AppColors.textOnDark,
          onSurface: AppColors.textPrimary,
          onError: AppColors.textOnDark,
        ),

        // AppBar styling
        // Styling cho AppBar
        appBarTheme: const AppBarTheme(
          backgroundColor: Colors.transparent,
          elevation: 0,
          foregroundColor: AppColors.textPrimary,
          iconTheme: IconThemeData(color: AppColors.textPrimary),
          titleTextStyle: TextStyle(
            color: AppColors.textPrimary,
            fontSize: 20,
            fontWeight: FontWeight.w600,
          ),
        ),

        // Button themes - Cinema Modern styling
        // Theme cho các nút với Cinema Modern styling
        elevatedButtonTheme: ElevatedButtonThemeData(
          style: ElevatedButton.styleFrom(
            backgroundColor: AppColors.buttonPrimary,
            foregroundColor: AppColors.textOnDark,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
            elevation: 2,
          ),
        ),
        textButtonTheme: TextButtonThemeData(
          style: TextButton.styleFrom(
            foregroundColor: AppColors.primaryRed,
          ),
        ),
        outlinedButtonTheme: OutlinedButtonThemeData(
          style: OutlinedButton.styleFrom(
            foregroundColor: AppColors.textPrimary,
            side: const BorderSide(color: AppColors.borderSecondary),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
          ),
        ),

        // Input field styling
        // Styling cho các trường nhập liệu
        inputDecorationTheme: const InputDecorationTheme(
          labelStyle: TextStyle(color: AppColors.textSecondary),
          hintStyle: TextStyle(color: AppColors.textTertiary),
          enabledBorder: OutlineInputBorder(
            borderSide: BorderSide(color: AppColors.borderPrimary),
            borderRadius: BorderRadius.all(Radius.circular(10)),
          ),
          focusedBorder: OutlineInputBorder(
            borderSide: BorderSide(color: AppColors.borderAccent),
            borderRadius: BorderRadius.all(Radius.circular(10)),
          ),
          errorBorder: OutlineInputBorder(
            borderSide: BorderSide(color: AppColors.errorRed),
            borderRadius: BorderRadius.all(Radius.circular(10)),
          ),
          focusedErrorBorder: OutlineInputBorder(
            borderSide: BorderSide(color: AppColors.errorRed),
            borderRadius: BorderRadius.all(Radius.circular(10)),
          ),
        ),

        // Chip and SnackBar themes - Cinema Modern
        // Theme cho Chip và SnackBar với Cinema Modern styling
        chipTheme: const ChipThemeData(
          backgroundColor: AppColors.surfaceColor,
          labelStyle: TextStyle(color: AppColors.textPrimary),
          selectedColor: AppColors.primaryRed,
          disabledColor: AppColors.textDisabled,
        ),
        snackBarTheme: const SnackBarThemeData(
          backgroundColor: AppColors.surfaceColor,
          contentTextStyle: TextStyle(color: AppColors.textPrimary),
        ),
      ),
      home: const AuthCheckPage(),
    );
  }
}

/// Authentication check page that determines app flow
/// Trang kiểm tra xác thực để xác định luồng ứng dụng
class AuthCheckPage extends StatefulWidget {
  const AuthCheckPage({Key? key}) : super(key: key);

  @override
  State<AuthCheckPage> createState() => _AuthCheckPageState();
}

class _AuthCheckPageState extends State<AuthCheckPage> {
  // Always show splash screen first
  // Luôn hiển thị splash screen trước
  final bool _showSplash = true;

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final authController = Get.find<AuthController>();

    // Show splash screen with movie banners before login
    // Hiển thị splash screen với banner phim trước khi đăng nhập
    if (_showSplash) {
      return const SplashScreen(
        nextScreen: LoginPage(),
      );
    }

    return _buildAuthScreen(authController);
  }

  /// Build appropriate screen based on authentication status
  /// Xây dựng màn hình phù hợp dựa trên trạng thái xác thực
  Widget _buildAuthScreen(AuthController authController) {
    return Obx(() {
      if (authController.isLoading) {
        // Show loading indicator while checking auth status
        // Hiển thị loading khi đang kiểm tra trạng thái xác thực
        return const Scaffold(
          body: Center(
            child: CircularProgressIndicator(),
          ),
        );
      } else if (authController.isLoggedIn) {
        // User is logged in, go to main app
        // Người dùng đã đăng nhập, chuyển đến ứng dụng chính
        return RootPage(i: 0);
      } else {
        // User not logged in, show login page
        // Người dùng chưa đăng nhập, hiển thị trang đăng nhập
        return const LoginPage();
      }
    });
  }
}
