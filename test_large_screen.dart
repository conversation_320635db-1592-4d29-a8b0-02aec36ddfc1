// Script để tạo một screen có nhiều ghế để test hiệu suất render
import 'package:cloud_firestore/cloud_firestore.dart';

void main() async {
  // Tạo một screen lớn với 300 ghế (20 hàng x 15 ghế/hàng)
  final largeScreen = {
    'name': 'IMAX Screen - Large Test',
    'theaterId': 'E0onGCUOdaPK1dwAAmvp', // CGV Aeon Mall Bình Tân
    'capacity': 300,
    'isActive': true,
    'seatLayout': _generateLargeSeatLayout(),
    'createdAt': FieldValue.serverTimestamp(),
    'updatedAt': FieldValue.serverTimestamp(),
  };

  print('Generated large screen with ${largeScreen['capacity']} seats');
  print('Seat layout has ${(largeScreen['seatLayout'] as List).length} rows');
  
  // In ra một phần của layout để kiểm tra
  final layout = largeScreen['seatLayout'] as List;
  print('First row: ${layout[0]}');
  print('Last row: ${layout[layout.length - 1]}');
}

List<Map<String, dynamic>> _generateLargeSeatLayout() {
  final layout = <Map<String, dynamic>>[];
  
  // Tạo 20 hàng ghế từ A đến T
  final rows = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 
                'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T'];
  
  for (int i = 0; i < rows.length; i++) {
    final rowLetter = rows[i];
    final seats = <Map<String, dynamic>>[];
    
    // Tạo 15 ghế cho mỗi hàng
    for (int j = 1; j <= 15; j++) {
      String seatType;
      
      // Phân loại ghế theo vị trí
      if (i < 3) {
        seatType = 'vip'; // 3 hàng đầu là VIP
      } else if (i >= rows.length - 2) {
        seatType = 'couple'; // 2 hàng cuối là couple
      } else {
        seatType = 'regular'; // Các hàng giữa là regular
      }
      
      seats.add({
        'number': j.toString().padLeft(2, '0'),
        'type': seatType,
        'isAvailable': true,
        'price': _getSeatPrice(seatType),
      });
    }
    
    layout.add({
      'row': rowLetter,
      'seats': seats,
    });
  }
  
  return layout;
}

double _getSeatPrice(String seatType) {
  switch (seatType) {
    case 'vip':
      return 150000.0;
    case 'couple':
      return 200000.0;
    case 'regular':
    default:
      return 100000.0;
  }
}
