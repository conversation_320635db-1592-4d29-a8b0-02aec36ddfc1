const functions = require('firebase-functions');
const admin = require('firebase-admin');

// Initialize Firebase Admin SDK
admin.initializeApp();

// Cloud Function to automatically update expired tickets
// Runs every hour
exports.updateExpiredTickets = functions.pubsub
  .schedule('0 * * * *') // Every hour at minute 0
  .timeZone('Asia/Ho_Chi_Minh') // Vietnam timezone
  .onRun(async (context) => {
    console.log('Starting expired tickets update...');
    
    try {
      const firestore = admin.firestore();
      const now = new Date();
      
      // Format current date and time for comparison
      const currentDate = now.toISOString().split('T')[0]; // YYYY-MM-DD
      const currentTime = now.toTimeString().split(' ')[0].substring(0, 5); // HH:MM
      
      console.log(`Current date: ${currentDate}, Current time: ${currentTime}`);
      
      // Query for tickets that should be expired
      // 1. Tickets with date before today
      const pastDateQuery = firestore
        .collection('tickets')
        .where('status', '==', 'confirmed')
        .where('date', '<', currentDate);
      
      // 2. Tickets for today but with time that has passed
      const todayQuery = firestore
        .collection('tickets')
        .where('status', '==', 'confirmed')
        .where('date', '==', currentDate)
        .where('time', '<', currentTime);
      
      const [pastDateSnapshot, todaySnapshot] = await Promise.all([
        pastDateQuery.get(),
        todayQuery.get()
      ]);
      
      const expiredTickets = [...pastDateSnapshot.docs, ...todaySnapshot.docs];
      
      console.log(`Found ${expiredTickets.length} tickets to expire`);
      
      // Update tickets in batches
      const batch = firestore.batch();
      let batchCount = 0;
      const maxBatchSize = 500; // Firestore batch limit
      
      for (const doc of expiredTickets) {
        if (batchCount >= maxBatchSize) {
          // Commit current batch and start a new one
          await batch.commit();
          batchCount = 0;
        }
        
        batch.update(doc.ref, {
          status: 'expired',
          updatedAt: admin.firestore.FieldValue.serverTimestamp()
        });
        
        batchCount++;
        console.log(`Marked ticket ${doc.id} as expired`);
      }
      
      // Commit the final batch
      if (batchCount > 0) {
        await batch.commit();
      }
      
      console.log(`Successfully updated ${expiredTickets.length} expired tickets`);
      
      return {
        success: true,
        updatedCount: expiredTickets.length,
        timestamp: now.toISOString()
      };
      
    } catch (error) {
      console.error('Error updating expired tickets:', error);
      throw new functions.https.HttpsError('internal', 'Failed to update expired tickets');
    }
  });

// HTTP function to manually trigger expired tickets update
exports.manualUpdateExpiredTickets = functions.https.onCall(async (data, context) => {
  // Check if user is authenticated and has admin privileges
  if (!context.auth) {
    throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
  }
  
  try {
    // Get user's custom claims to check if they're admin
    const userRecord = await admin.auth().getUser(context.auth.uid);
    const customClaims = userRecord.customClaims || {};
    
    if (!customClaims.admin && !customClaims.developer) {
      throw new functions.https.HttpsError('permission-denied', 'User must be admin or developer');
    }
    
    console.log(`Manual expired tickets update triggered by ${context.auth.uid}`);
    
    const firestore = admin.firestore();
    const now = new Date();
    
    // Format current date and time for comparison
    const currentDate = now.toISOString().split('T')[0]; // YYYY-MM-DD
    const currentTime = now.toTimeString().split(' ')[0].substring(0, 5); // HH:MM
    
    console.log(`Current date: ${currentDate}, Current time: ${currentTime}`);
    
    // Query for tickets that should be expired
    const pastDateQuery = firestore
      .collection('tickets')
      .where('status', '==', 'confirmed')
      .where('date', '<', currentDate);
    
    const todayQuery = firestore
      .collection('tickets')
      .where('status', '==', 'confirmed')
      .where('date', '==', currentDate)
      .where('time', '<', currentTime);
    
    const [pastDateSnapshot, todaySnapshot] = await Promise.all([
      pastDateQuery.get(),
      todayQuery.get()
    ]);
    
    const expiredTickets = [...pastDateSnapshot.docs, ...todaySnapshot.docs];
    
    console.log(`Found ${expiredTickets.length} tickets to expire`);
    
    // Update tickets in batches
    const batch = firestore.batch();
    let batchCount = 0;
    const maxBatchSize = 500;
    
    for (const doc of expiredTickets) {
      if (batchCount >= maxBatchSize) {
        await batch.commit();
        batchCount = 0;
      }
      
      batch.update(doc.ref, {
        status: 'expired',
        updatedAt: admin.firestore.FieldValue.serverTimestamp()
      });
      
      batchCount++;
    }
    
    if (batchCount > 0) {
      await batch.commit();
    }
    
    console.log(`Successfully updated ${expiredTickets.length} expired tickets`);
    
    return {
      success: true,
      updatedCount: expiredTickets.length,
      timestamp: now.toISOString()
    };
    
  } catch (error) {
    console.error('Error in manual update:', error);
    throw new functions.https.HttpsError('internal', 'Failed to update expired tickets');
  }
});

// Function to get expired tickets statistics
exports.getExpiredTicketsStats = functions.https.onCall(async (data, context) => {
  if (!context.auth) {
    throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
  }
  
  try {
    const userRecord = await admin.auth().getUser(context.auth.uid);
    const customClaims = userRecord.customClaims || {};
    
    if (!customClaims.admin && !customClaims.developer) {
      throw new functions.https.HttpsError('permission-denied', 'User must be admin or developer');
    }
    
    const firestore = admin.firestore();
    const now = new Date();
    const currentDate = now.toISOString().split('T')[0];
    const currentTime = now.toTimeString().split(' ')[0].substring(0, 5);
    
    // Get various ticket counts
    const [confirmedSnapshot, expiredSnapshot, shouldBeExpiredSnapshot] = await Promise.all([
      firestore.collection('tickets').where('status', '==', 'confirmed').get(),
      firestore.collection('tickets').where('status', '==', 'expired').get(),
      firestore.collection('tickets')
        .where('status', '==', 'confirmed')
        .where('date', '<', currentDate)
        .get()
    ]);
    
    return {
      totalConfirmed: confirmedSnapshot.size,
      totalExpired: expiredSnapshot.size,
      shouldBeExpired: shouldBeExpiredSnapshot.size,
      lastCheck: now.toISOString()
    };
    
  } catch (error) {
    console.error('Error getting stats:', error);
    throw new functions.https.HttpsError('internal', 'Failed to get statistics');
  }
});
