import 'package:flutter/material.dart';

/// Centralized color management for the Đớp Phim app
/// Material Design 3 inspired theme với màu sắc mềm mại và dễ nhìn
/// <PERSON><PERSON> thủ nguyên tắc accessibility và contrast của Material Design
class AppColors {
  // Private constructor to prevent instantiation
  AppColors._();

  // Primary gradient colors - Material Design 3 inspired
  static const Color primaryGradientStart =
      Color(0xFF6750A4); // Tím mềm Material Design
  static const Color primaryGradientEnd = Color(0xFF7C4DFF); // Tím accent
  static const Color primaryGradientAccent = Color(0xFFBB86FC); // Tím sáng

  // Background colors - Material Design 3 dark theme
  static const Color scaffoldBackground =
      Color(0xFF121212); // Nền tối chuẩn Material
  static const Color cardBackground = Color(0xFF1E1E1E); // Card tối
  static const Color surfaceColor = Color(0xFF2C2C2C); // Surface tối

  // Primary colors - Material Design 3 palette
  static const Color primaryPurple = Color(0xFF6750A4); // Tím chính mềm mại
  static const Color primaryAccent = Color(0xFFBB86FC); // Tím accent sáng
  static const Color primaryWhite = Color(0xFFF5F5F5); // Trắng kem dễ nhìn

  // Accent colors - Material Design 3 tones
  static const Color accentPurple = Color(0xFF7C4DFF); // Tím accent
  static const Color accentTeal = Color(0xFF03DAC6); // Xanh teal Material
  static const Color accentWarmGray = Color(0xFF3C3C3C); // Xám ấm

  // Status colors - Material Design 3 status colors
  static const Color successGreen = Color(0xFF4CAF50); // Xanh lá thành công
  static const Color warningOrange = Color(0xFFFF9800); // Cam cảnh báo mềm
  static const Color errorRed = Color(0xFFCF6679); // Đỏ lỗi mềm Material
  static const Color infoBlue = Color(0xFF03DAC6); // Xanh teal thông tin

  // Text colors - Material Design 3 text hierarchy
  static const Color textPrimary = Color(0xFFE6E1E5); // Text chính Material
  static const Color textSecondary = Color(0xFFCAC4D0); // Text phụ Material
  static const Color textTertiary = Color(0xFF938F99); // Text tertiary Material
  static const Color textDisabled = Color(0xFF625B71); // Text disabled Material
  static const Color textOnDark = Color(0xFFE6E1E5); // Text trên nền tối
  static const Color textOnLight = Color(0xFF1C1B1F); // Text trên nền sáng

  // Button colors - Material Design 3 button colors
  static const Color buttonPrimary = Color(0xFF6750A4); // Tím chính cho button
  static const Color buttonSecondary =
      Color(0xFF7C4DFF); // Tím accent cho button phụ
  static const Color buttonDanger = Color(0xFFCF6679); // Đỏ nguy hiểm mềm
  static const Color buttonSuccess = Color(0xFF4CAF50); // Xanh thành công
  static const Color buttonWarning = Color(0xFFFF9800); // Cam cảnh báo
  static const Color buttonAccent = Color(0xFF03DAC6); // Xanh teal accent

  // Border colors - Material Design 3 outline colors
  static const Color borderPrimary = Color(0xFF49454F); // Border chính Material
  static const Color borderSecondary = Color(0xFF625B71); // Border phụ Material
  static const Color borderAccent =
      Color(0xFF6750A4); // Tím chính cho border accent

  // Overlay colors - Điều chỉnh cho cinema theme
  static const Color overlayLight = Color(0x1AFFFFFF);
  static const Color overlayMedium = Color(0x33FFFFFF);
  static const Color overlayDark = Color(0x66000000);
  static const Color overlayRed = Color(0x33DC143C); // Overlay đỏ cinema

  // Chat/Message colors - Cinema theme cho bug reports và notifications
  static const Color chatSystemBackground =
      Color(0xFF8B0000); // Đỏ burgundy cho system
  static const Color chatDeveloperBackground =
      Color(0xFFDC143C); // Đỏ cinema cho developer
  static const Color chatAdminBackground = Color(0xFF4CAF50); // Xanh cho admin
  static const Color chatUserBackground = Color(0xFF3C3C3C); // Xám ấm cho user
  static const Color chatCurrentUserBackground =
      Color(0xFF800020); // Đỏ đậm cho current user

  // Screen type colors - Cinema theme
  static const Color screenStandard =
      Color(0xFFDC143C); // Đỏ cinema cho standard
  static const Color screenVip = Color(0xFFFFD700); // Vàng gold cho VIP
  static const Color screenImax = Color(0xFF8B0000); // Đỏ burgundy cho IMAX
  static const Color screenDolby = Color(0xFF4CAF50); // Xanh cho Dolby
  static const Color screenPremium = Color(0xFFFF8C00); // Cam cho Premium

  // Seat colors - Tối ưu cho cinema theme
  static const Color seatAvailable =
      Color(0x4DFFFFFF); // Trắng mờ cho ghế trống
  static const Color seatSelected =
      Color(0xFFDC143C); // Đỏ cinema cho ghế đã chọn
  static const Color seatBooked = Color(0xFF757575); // Xám cho ghế đã đặt
  static const Color seatVip = Color(0xFFFFD700); // Vàng gold cho ghế VIP
  static const Color seatCouple = Color(0xFFFF69B4); // Hồng cho ghế đôi
  static const Color seatDisabled =
      Color(0xFF424242); // Xám đậm cho ghế disabled

  // Genre selection colors - Cinema theme
  static const Color genreSelected =
      Color(0xFFFFD700); // Vàng gold cho genre đã chọn
  static const Color genreUnselected =
      Color(0x1EDC143C); // Đỏ mờ cho genre chưa chọn

  // Status chip colors - Cinema theme
  static const Color statusPending = Color(0xFF9E9E9E); // Xám cho pending
  static const Color statusAccepted =
      Color(0xFFDC143C); // Đỏ cinema cho accepted
  static const Color statusInProgress =
      Color(0xFFFF8C00); // Cam cho in progress
  static const Color statusFixed = Color(0xFF4CAF50); // Xanh cho fixed

  // Backward compatibility - Giữ tên cũ để không phá vỡ code hiện tại
  static const Color primaryBlue =
      primaryPurple; // Thay primaryBlue bằng primaryPurple
  static const Color primaryAmber =
      primaryAccent; // Thay primaryAmber bằng primaryAccent

  // Common gradients - Cinema Modern theme
  static const LinearGradient primaryGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [primaryGradientStart, primaryGradientEnd, primaryGradientAccent],
    stops: [0.0, 0.5, 1.0],
  );

  static const LinearGradient primaryGradientVertical = LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: [primaryGradientStart, primaryGradientEnd, primaryGradientAccent],
    stops: [0.0, 0.5, 1.0],
  );

  // Button gradient - Cinema theme
  static const LinearGradient buttonGradient = LinearGradient(
    colors: [primaryGradientStart, primaryGradientEnd],
  );

  // Cinema overlay gradient
  static const LinearGradient overlayGradient = LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: [
      Colors.transparent,
      Color(0x4D8B0000), // Đỏ burgundy mờ
      Color(0x4DDC143C), // Đỏ cinema mờ
      Color(0xB3800020), // Đỏ đậm
    ],
  );

  // Genre gradients - Cinema theme
  static const LinearGradient genreSelectedGradient = LinearGradient(
    colors: [Color(0xFFFFD700), Color(0xFFFFB74D)], // Vàng gold gradient
  );

  static const LinearGradient genreUnselectedGradient = LinearGradient(
    colors: [Color(0x1EDC143C), Color(0x1E8B0000)], // Đỏ mờ gradient
  );

  // Helper methods
  static Color withOpacity(Color color, double opacity) {
    return color.withOpacity(opacity);
  }

  static Color getChatBackgroundColor({
    required String responderId,
    required bool isFromDeveloper,
    required bool isFromAdmin,
    required bool isCurrentUser,
  }) {
    if (responderId == 'system') {
      return chatSystemBackground.withOpacity(0.3);
    } else if (isFromDeveloper) {
      return chatDeveloperBackground.withOpacity(0.8);
    } else if (isFromAdmin) {
      return chatAdminBackground.withOpacity(0.8);
    } else {
      return isCurrentUser
          ? chatCurrentUserBackground.withOpacity(0.6)
          : chatUserBackground.withOpacity(0.4);
    }
  }

  static Color getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return statusPending;
      case 'accepted':
        return statusAccepted;
      case 'inprogress':
      case 'in_progress':
        return statusInProgress;
      case 'fixed':
        return statusFixed;
      default:
        return statusPending;
    }
  }

  static Color getScreenTypeColor(String screenType) {
    switch (screenType.toLowerCase()) {
      case 'standard':
        return screenStandard;
      case 'vip':
        return screenVip;
      case 'imax':
        return screenImax;
      case 'dolby':
        return screenDolby;
      case 'premium':
        return screenPremium;
      default:
        return screenStandard;
    }
  }
}
