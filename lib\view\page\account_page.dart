import 'dart:async';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../controllers/auth_controller.dart';
import '../../controllers/language_controller.dart';
import '../../utils/developer_mode.dart';
import '../../utils/app_colors.dart';
import 'auth/login_page.dart';

class AccountPage extends StatefulWidget {
  const AccountPage({Key? key}) : super(key: key);

  @override
  State<AccountPage> createState() => _AccountPageState();
}

class _AccountPageState extends State<AccountPage> {
  final authController = Get.find<AuthController>();
  final languageController = Get.find<LanguageController>();

  // Counter for admin access
  int _adminTapCount = 0;

  // Timer for resetting tap count
  Timer? _resetTimer;

  @override
  void dispose() {
    _resetTimer?.cancel();
    super.dispose();
  }

  // Handle tap on username for admin access
  void _handleUsernameTap() {
    setState(() {
      _adminTapCount++;
    });

    // Reset the counter after 3 seconds of inactivity
    _resetTimer?.cancel();
    _resetTimer = Timer(const Duration(seconds: 3), () {
      setState(() {
        _adminTapCount = 0;
      });
    });

    // Check if user has tapped 7 times
    if (_adminTapCount >= 7) {
      _adminTapCount = 0;
      _resetTimer?.cancel();

      // Navigate to admin login page
      Get.toNamed('/admin');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      final user = authController.user;

      return Container(
        decoration: const BoxDecoration(
          gradient: AppColors.primaryGradientVertical,
        ),
        child: SafeArea(
          child: SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsets.all(20.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  const SizedBox(height: 20),
                  Text(
                    'profile'.tr,
                    style: GoogleFonts.mulish(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                  const SizedBox(height: 30),
                  Stack(
                    children: [
                      CircleAvatar(
                        radius: 60,
                        backgroundColor: Colors.white24,
                        backgroundImage: user?.photoUrl != null
                            ? NetworkImage(user!.photoUrl!)
                            : null,
                        child: user?.photoUrl == null
                            ? Icon(
                                Icons.person,
                                size: 60,
                                color: Colors.white.withOpacity(0.7),
                              )
                            : null,
                      ),
                      if (authController.isLoggedIn)
                        Positioned(
                          bottom: 0,
                          right: 0,
                          child: GestureDetector(
                            onTap: () => Get.toNamed('/profile/edit'),
                            child: Container(
                              padding: const EdgeInsets.all(8),
                              decoration: BoxDecoration(
                                gradient: AppColors.buttonGradient,
                                shape: BoxShape.circle,
                                border: Border.all(
                                  color: Colors.white,
                                  width: 2,
                                ),
                              ),
                              child: const Icon(
                                Icons.edit,
                                color: Colors.white,
                                size: 16,
                              ),
                            ),
                          ),
                        ),
                    ],
                  ),
                  const SizedBox(height: 20),
                  // Username with GestureDetector for admin access
                  GestureDetector(
                    onTap: _handleUsernameTap,
                    child: Text(
                      user?.name ?? 'Guest User',
                      style: GoogleFonts.mulish(
                        fontSize: 22,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ),
                  const SizedBox(height: 5),
                  Text(
                    user?.email ?? 'Not signed in',
                    style: GoogleFonts.mulish(
                      fontSize: 16,
                      color: Colors.white70,
                    ),
                  ),
                  const SizedBox(height: 40),
                  _buildProfileItem(
                    icon: Icons.lock_outline,
                    title: 'Đổi mật khẩu',
                    onTap: () {
                      Get.toNamed('/change_password');
                    },
                  ),
                  _buildProfileItem(
                    icon: Icons.help_outline,
                    title: 'help'.tr,
                    onTap: () {
                      Get.snackbar(
                        'Coming Soon',
                        'This feature is under development',
                        snackPosition: SnackPosition.BOTTOM,
                      );
                    },
                  ),
                  _buildProfileItem(
                    icon: Icons.bug_report,
                    title: 'Bug Issues',
                    onTap: () {
                      Get.toNamed('/realtime_notifications');
                    },
                  ),
                  _buildProfileItem(
                    icon: Icons.language,
                    title: 'change_language'.tr,
                    onTap: () {
                      // Toggle language without navigating away from current screen
                      languageController.toggleLanguage();

                      // Show a snackbar to confirm language change
                      Get.snackbar(
                        'language'.tr,
                        languageController.isEnglish
                            ? 'vietnamese'.tr
                            : 'english'.tr,
                        snackPosition: SnackPosition.BOTTOM,
                        duration: const Duration(seconds: 2),
                      );
                    },
                  ),
                  const SizedBox(height: 20),
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: () async {
                        await authController.logout();
                        Get.offAll(() => const LoginPage());
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.transparent,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        elevation: 0,
                      ).copyWith(
                        backgroundColor:
                            WidgetStateProperty.all(Colors.transparent),
                      ),
                      child: Container(
                        width: double.infinity,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        decoration: BoxDecoration(
                          gradient: const LinearGradient(
                            colors: [Colors.redAccent, Colors.red],
                          ),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          'logout'.tr,
                          textAlign: TextAlign.center,
                          style: GoogleFonts.mulish(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(height: 20),
                  // Admin button - only visible for admin users
                  if (authController.isAdmin)
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: () {
                          Get.toNamed('/admin/home');
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.transparent,
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          elevation: 0,
                        ).copyWith(
                          backgroundColor:
                              WidgetStateProperty.all(Colors.transparent),
                        ),
                        child: Container(
                          width: double.infinity,
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          decoration: BoxDecoration(
                            gradient: AppColors.buttonGradient,
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            'Admin Dashboard',
                            textAlign: TextAlign.center,
                            style: GoogleFonts.mulish(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                          ),
                        ),
                      ),
                    ),

                  const SizedBox(height: 20),
                  // Debug button - only visible for developer accounts
                  Obx(() {
                    final developerMode = Get.find<DeveloperMode>();
                    final isDev = developerMode.isDeveloperMode;

                    // Chỉ hiển thị cho tài khoản admin và developer
                    if (developerMode.hasAdminAccess()) {
                      return Column(
                        children: [
                          // Hiển thị trạng thái developer
                          Container(
                            width: double.infinity,
                            padding: const EdgeInsets.all(8),
                            margin: const EdgeInsets.only(bottom: 8),
                            decoration: BoxDecoration(
                              color: isDev
                                  ? Colors.green.withOpacity(0.2)
                                  : Colors.red.withOpacity(0.2),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Text(
                              'Developer Mode: ${isDev ? "ENABLED" : "DISABLED"}',
                              textAlign: TextAlign.center,
                              style: TextStyle(
                                color: isDev ? Colors.green : Colors.red,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),

                          // Nút Debug - chỉ hiển thị cho developer
                          if (isDev)
                            SizedBox(
                              width: double.infinity,
                              child: OutlinedButton(
                                onPressed: () {
                                  Get.toNamed('/debug');
                                },
                                style: OutlinedButton.styleFrom(
                                  padding:
                                      const EdgeInsets.symmetric(vertical: 16),
                                  side: const BorderSide(color: Colors.grey),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(10),
                                  ),
                                ),
                                child: const Text(
                                  'Debug Firestore',
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                            ),
                        ],
                      );
                    } else {
                      // Không hiển thị gì cho người dùng thông thường
                      return const SizedBox.shrink();
                    }
                  }),
                ],
              ),
            ),
          ),
        ),
      );
    });
  }

  Widget _buildProfileItem({
    required IconData icon,
    required String title,
    required VoidCallback onTap,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16.0),
      child: ListTile(
        onTap: onTap,
        leading: Container(
          padding: const EdgeInsets.all(10),
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.1),
            borderRadius: BorderRadius.circular(10),
          ),
          child: Icon(
            icon,
            color: Colors.white,
          ),
        ),
        title: Text(
          title,
          style: GoogleFonts.mulish(
            fontSize: 16,
            color: Colors.white,
          ),
        ),
        trailing: const Icon(
          Icons.arrow_forward_ios,
          color: Colors.white70,
          size: 16,
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
        tileColor: Colors.white.withOpacity(0.05),
      ),
    );
  }
}
