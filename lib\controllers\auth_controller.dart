/// Controller xác thực để quản lý đăng nhập, đăng ký và quyền người dùng
import 'package:get/get.dart';
import '../models/user_model.dart';
import '../services/auth_service.dart';
import '../services/storage_service.dart';
import '../services/controller_initializer.dart';
import '../utils/storage_cleanup.dart';
import '../utils/developer_mode.dart';

class AuthController extends GetxController {
  // Các instance service cho xác thực và lưu trữ
  final AuthService _authService = AuthService();
  final StorageService _storageService = StorageService();

  // Các biến trạng thái reactive
  final Rx<UserModel?> _user = Rx<UserModel?>(null);
  final RxBool _isLoading = false.obs;
  final RxBool _isLoggedIn = false.obs;
  final RxString _errorMessage = ''.obs;

  // Getters để truy cập trạng thái
  UserModel? get user => _user.value;
  Rx<UserModel?> get userRx => _user; // Đối tượng user reactive
  bool get isLoading => _isLoading.value;
  bool get isLoggedIn => _isLoggedIn.value;
  RxBool get isLoggedInObs => _isLoggedIn;
  String get errorMessage => _errorMessage.value;

  /// Lấy vai trò của người dùng hiện tại (user/admin/developer)
  String get userRole {
    // Kiểm tra developer trước (ưu tiên cao nhất)
    try {
      final developerMode = Get.find<DeveloperMode>();
      if (developerMode.isDeveloper()) {
        return 'developer';
      }
    } catch (e) {
      // Bỏ qua lỗi nếu DeveloperMode chưa được khởi tạo
    }

    // Kiểm tra vai trò admin từ UserModel
    if (_user.value?.isAdmin == true) {
      return 'admin';
    }

    return 'user';
  }

  /// Kiểm tra xem người dùng hiện tại có quyền admin không (admin hoặc developer)
  bool get isAdmin {
    // Developer luôn có quyền admin
    try {
      final developerMode = Get.find<DeveloperMode>();
      if (developerMode.isDeveloper()) {
        return true;
      }
    } catch (e) {
      // Bỏ qua lỗi nếu DeveloperMode chưa được khởi tạo
    }

    // Kiểm tra quyền admin thông thường
    return _user.value?.isAdmin ?? false;
  }

  @override
  void onInit() {
    super.onInit();
    checkLoginStatus();
  }

  /// Xóa bắt buộc tất cả dữ liệu đã lưu (dùng để khôi phục lỗi)
  Future<void> _forceClearAllData() async {
    try {
      await _storageService.clearAll();
      await StorageCleanup.clearAllData();
      await StorageCleanup.checkAndFixProblematicData();
    } catch (e) {
      // Bỏ qua lỗi trong quá trình dọn dẹp để tránh vòng lặp vô hạn
    }
  }

  /// Kiểm tra xem người dùng hiện tại có đăng nhập không và tải dữ liệu người dùng
  Future<void> checkLoginStatus() async {
    _isLoading.value = true;
    try {
      _isLoggedIn.value = await _authService.isLoggedIn();
      if (_isLoggedIn.value) {
        try {
          _user.value = await _authService.getCurrentUser();
          // Các controllers đã được khởi tạo trong main.dart
        } catch (e) {
          if (e.toString().contains('PigeonUserDetails')) {
            // Xử lý lỗi cụ thể của Google Sign-In
            await _forceClearAllData();
            _isLoggedIn.value = false;
            _user.value = null;
          } else {
            _errorMessage.value = _formatErrorMessage(e);
          }
        }
      }
    } catch (e) {
      await _forceClearAllData();
      _isLoggedIn.value = false;
      _errorMessage.value = _formatErrorMessage(e);
    } finally {
      _isLoading.value = false;
    }
  }

  /// Login user with email and password
  /// Đăng nhập người dùng bằng email và mật khẩu
  Future<bool> login(String email, String password) async {
    _isLoading.value = true;
    _errorMessage.value = '';

    try {
      try {
        final user = await _authService.login(email, password);
        if (user != null) {
          _user.value = user;
          _isLoggedIn.value = true;
          return true;
        }
      } catch (firstAttemptError) {
        if (firstAttemptError.toString().contains('PigeonUserDetails')) {
          // Clear data and retry for Google Sign-In issues
          // Xóa dữ liệu và thử lại cho vấn đề Google Sign-In
          await _forceClearAllData();
        } else if (firstAttemptError
            .toString()
            .contains('cloud_firestore/permission-denied')) {
          // Firestore permission error can be handled gracefully
          // Lỗi quyền Firestore có thể được xử lý một cách nhẹ nhàng
        } else {
          rethrow;
        }
      }

      // Retry login after clearing data
      // Thử đăng nhập lại sau khi xóa dữ liệu
      final user = await _authService.login(email, password);
      if (user != null) {
        _user.value = user;
        _isLoggedIn.value = true;
        // Controllers are already initialized in main.dart
        // Các controllers đã được khởi tạo trong main.dart
        return true;
      } else {
        _errorMessage.value = 'Invalid email or password';
        return false;
      }
    } catch (e) {
      // Handle specific Firebase Auth errors with user-friendly messages
      // Xử lý các lỗi Firebase Auth cụ thể với thông báo thân thiện
      if (e.toString().contains('user-not-found')) {
        _errorMessage.value =
            'No user found with this email. Please register first.';
      } else if (e.toString().contains('wrong-password')) {
        _errorMessage.value = 'Incorrect password. Please try again.';
      } else if (e.toString().contains('too-many-requests')) {
        _errorMessage.value =
            'Too many failed login attempts. Please try again later.';
      } else if (e.toString().contains('network-request-failed')) {
        _errorMessage.value =
            'Network error. Please check your internet connection.';
      } else if (e.toString().contains('cloud_firestore/permission-denied')) {
        // Allow login with limited functionality
        // Cho phép đăng nhập với chức năng hạn chế
        _errorMessage.value =
            'Logged in with limited functionality. Some features may not be available.';
        return true;
      } else {
        _errorMessage.value = _formatErrorMessage(e);
      }
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  Future<bool> register(String email, String password, String name) async {
    _isLoading.value = true;
    _errorMessage.value = '';

    try {
      try {
        final user = await _authService.register(email, password, name);
        if (user != null) {
          _user.value = user;
          _isLoggedIn.value = true;
          return true;
        }
      } catch (firstAttemptError) {
        if (firstAttemptError.toString().contains('PigeonUserDetails')) {
          // Clear data and retry
          await _forceClearAllData();
        } else if (firstAttemptError
            .toString()
            .contains('cloud_firestore/permission-denied')) {
          // If we get a Firestore permission error, we can still proceed
          // The modified AuthService will handle this gracefully
        } else {
          rethrow;
        }
      }
      final user = await _authService.register(email, password, name);
      if (user != null) {
        _user.value = user;
        _isLoggedIn.value = true;

        // Controllers are already initialized in main.dart

        return true;
      } else {
        _errorMessage.value = 'Registration failed';
        return false;
      }
    } catch (e) {
      if (e.toString().contains('email-already-in-use')) {
        _errorMessage.value =
            'This email is already registered. Please login instead.';
      } else if (e.toString().contains('weak-password')) {
        _errorMessage.value =
            'Password is too weak. Please use a stronger password.';
      } else if (e.toString().contains('invalid-email')) {
        _errorMessage.value =
            'Invalid email format. Please enter a valid email.';
      } else if (e.toString().contains('network-request-failed')) {
        _errorMessage.value =
            'Network error. Please check your internet connection.';
      } else if (e.toString().contains('cloud_firestore/permission-denied')) {
        // If we get a Firestore permission error, we can still register
        // The user data will be stored locally only
        _errorMessage.value =
            'Registered with limited functionality. Some features may not be available.';
        return true;
      } else {
        _errorMessage.value = _formatErrorMessage(e);
      }
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  Future<void> logout() async {
    _isLoading.value = true;
    try {
      await _forceClearAllData();
      try {
        await _authService.logout();
      } catch (e) {
        // Ignore any errors during logout since we've already cleared data
      }

      // Controllers remain initialized

      _user.value = null;
      _isLoggedIn.value = false;
    } catch (e) {
      // Even if there's an error, still clear the local state
      _user.value = null;
      _isLoggedIn.value = false;
      _errorMessage.value = _formatErrorMessage(e);
    } finally {
      _isLoading.value = false;
    }
  }

  Future<bool> changePassword(
      String currentPassword, String newPassword) async {
    _isLoading.value = true;
    _errorMessage.value = '';

    try {
      await _authService.changePassword(currentPassword, newPassword);
      return true;
    } catch (e) {
      _errorMessage.value = _formatErrorMessage(e);
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  Future<bool> sendEmailVerification() async {
    _isLoading.value = true;
    _errorMessage.value = '';

    try {
      await _authService.sendEmailVerification();
      return true;
    } catch (e) {
      _errorMessage.value = _formatErrorMessage(e);
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  Future<bool> resetPassword(String email) async {
    _isLoading.value = true;
    _errorMessage.value = '';

    try {
      await _authService.resetPassword(email);
      return true;
    } catch (e) {
      if (e.toString().contains('user-not-found')) {
        _errorMessage.value = 'No user found with this email address.';
      } else if (e.toString().contains('invalid-email')) {
        _errorMessage.value = 'Please enter a valid email address.';
      } else if (e.toString().contains('network-request-failed')) {
        _errorMessage.value =
            'Network error. Please check your internet connection.';
      } else {
        _errorMessage.value = _formatErrorMessage(e);
      }
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// Format error message to be more user-friendly
  /// Định dạng thông báo lỗi để thân thiện hơn với người dùng
  String _formatErrorMessage(dynamic error) {
    String message = error.toString();

    // Extract message from Exception wrapper
    // Trích xuất thông báo từ wrapper Exception
    if (message.contains('Exception:')) {
      message = message.split('Exception:')[1].trim();
    }

    // Remove Firebase specific prefixes for cleaner messages
    // Xóa tiền tố cụ thể của Firebase để thông báo sạch hơn
    if (message.contains('[firebase_auth/')) {
      message = message.split(']')[1].trim();
    }

    return message;
  }

  /// Clear current error message
  /// Xóa thông báo lỗi hiện tại
  void clearError() {
    _errorMessage.value = '';
  }

  /// Diagnostic method to check Firebase authentication state
  /// Phương thức chẩn đoán để kiểm tra trạng thái xác thực Firebase
  Future<String> checkFirebaseAuthState() async {
    try {
      final currentUser = _authService.getCurrentFirebaseUser();
      if (currentUser != null) {
        return 'Firebase user is logged in: ${currentUser.email}';
      } else {
        return 'No Firebase user is logged in';
      }
    } catch (e) {
      return 'Error checking Firebase auth state: $e';
    }
  }

  // ===== ADMIN MANAGEMENT METHODS =====
  // ===== CÁC PHƯƠNG THỨC QUẢN LÝ ADMIN =====

  /// Check if a specific user has admin privileges
  /// Kiểm tra xem một người dùng cụ thể có quyền admin không
  Future<bool> isUserAdmin(String userId) async {
    try {
      return await _authService.isUserAdmin(userId);
    } catch (e) {
      _errorMessage.value = _formatErrorMessage(e);
      return false;
    }
  }

  /// Check if a specific user has developer privileges
  /// Kiểm tra xem một người dùng cụ thể có quyền developer không
  Future<bool> isUserDeveloper(String userId) async {
    try {
      return await _authService.isUserDeveloper(userId);
    } catch (e) {
      _errorMessage.value = _formatErrorMessage(e);
      return false;
    }
  }

  /// Set a user as admin
  /// Đặt một người dùng làm admin
  Future<bool> setUserAsAdmin(String userId) async {
    _isLoading.value = true;
    try {
      final result = await _authService.setUserAsAdmin(userId);

      // Update local user data if current user is being modified
      // Cập nhật dữ liệu người dùng local nếu người dùng hiện tại đang được sửa đổi
      if (_user.value?.id == userId && result) {
        _user.value = _user.value?.copyWith(role: UserRole.admin);
      }

      return result;
    } catch (e) {
      _errorMessage.value = _formatErrorMessage(e);
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// Set a user as regular user (remove admin privileges)
  /// Đặt một người dùng làm người dùng thường (xóa quyền admin)
  Future<bool> setUserAsRegular(String userId) async {
    _isLoading.value = true;
    try {
      final result = await _authService.setUserAsRegular(userId);

      // Update local user data if current user is being modified
      // Cập nhật dữ liệu người dùng local nếu người dùng hiện tại đang được sửa đổi
      if (_user.value?.id == userId && result) {
        _user.value = _user.value?.copyWith(role: UserRole.user);
      }

      return result;
    } catch (e) {
      _errorMessage.value = _formatErrorMessage(e);
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// Set a user as developer (highest privilege level)
  /// Đặt một người dùng làm developer (cấp quyền cao nhất)
  Future<bool> setUserAsDeveloper(String userId) async {
    _isLoading.value = true;
    try {
      final result = await _authService.setUserAsDeveloper(userId);

      // Update local user data if current user is being modified
      // Cập nhật dữ liệu người dùng local nếu người dùng hiện tại đang được sửa đổi
      if (_user.value?.id == userId && result) {
        _user.value = _user.value?.copyWith(role: UserRole.developer);
      }

      return result;
    } catch (e) {
      _errorMessage.value = _formatErrorMessage(e);
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// Get all users in the system (admin only)
  /// Lấy tất cả người dùng trong hệ thống (chỉ admin)
  Future<List<UserModel>> getAllUsers() async {
    _isLoading.value = true;
    try {
      return await _authService.getAllUsers();
    } catch (e) {
      _errorMessage.value = _formatErrorMessage(e);
      return [];
    } finally {
      _isLoading.value = false;
    }
  }

  /// Get list of all admin and developer user IDs for notifications
  /// Lấy danh sách ID của tất cả admin và developer để gửi thông báo
  Future<List<String>> getAdminAndDeveloperIds() async {
    try {
      // Get all users from the system
      // Lấy tất cả người dùng từ hệ thống
      final users = await getAllUsers();

      // Filter admin and developer users
      // Lọc ra các người dùng admin và developer
      final adminAndDevIds = users
          .where((user) => user
              .isAdmin) // isAdmin returns true for both admin and developer / isAdmin trả về true cho cả admin và developer
          .map((user) => user.id ?? '')
          .where((id) => id.isNotEmpty)
          .toList();

      return adminAndDevIds;
    } catch (e) {
      print('Error getting admin and developer IDs: $e');
      return [];
    }
  }

  /// Update current user's profile information
  /// Cập nhật thông tin hồ sơ của người dùng hiện tại
  Future<bool> updateProfile({String? name, String? photoUrl}) async {
    _isLoading.value = true;
    _errorMessage.value = '';

    try {
      final userId = _user.value?.id;
      if (userId == null) {
        _errorMessage.value = 'User not logged in';
        return false;
      }

      final result = await _authService.updateUserProfile(
        userId,
        name: name,
        photoUrl: photoUrl,
      );

      if (result) {
        // Update the local user object with new information
        // Cập nhật đối tượng người dùng local với thông tin mới
        final updatedUser = _user.value?.copyWith(
          name: name ?? _user.value?.name,
          photoUrl: photoUrl ?? _user.value?.photoUrl,
        );

        if (updatedUser != null) {
          _user.value = updatedUser;
        }

        return true;
      } else {
        _errorMessage.value = 'Failed to update profile';
        return false;
      }
    } catch (e) {
      _errorMessage.value = _formatErrorMessage(e);
      return false;
    } finally {
      _isLoading.value = false;
    }
  }
}
