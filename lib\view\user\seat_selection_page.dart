import 'dart:async';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../models/movie_model.dart';
import '../../models/theater_model.dart';
import '../../models/showtime_model.dart';
import '../../models/screen_model.dart';
import '../../services/screen_service.dart';
import '../../services/showtime_service.dart';
import '../../controllers/auth_controller.dart';
import '../../utils/app_colors.dart';
import 'payment_page.dart';

class SeatSelectionPage extends StatefulWidget {
  final Movie movie;
  final ShowtimeModel showtime;
  final TheaterModel theater;

  const SeatSelectionPage({
    Key? key,
    required this.movie,
    required this.showtime,
    required this.theater,
  }) : super(key: key);

  @override
  State<SeatSelectionPage> createState() => _SeatSelectionPageState();
}

class _SeatSelectionPageState extends State<SeatSelectionPage> {
  final ScreenService _screenService = ScreenService();
  final ShowtimeService _showtimeService = ShowtimeService();

  final Rx<ScreenModel?> _screen = Rx<ScreenModel?>(null);
  final RxList<String> _selectedSeats = <String>[].obs;
  final RxBool _isLoading = false.obs;
  final RxDouble _totalPrice = 0.0.obs;

  // Cache cho tối ưu hiệu suất
  final Map<String, Widget> _seatWidgetCache = {};
  Timer? _debounceTimer;

  @override
  void initState() {
    super.initState();
    _loadScreen();
  }

  @override
  void dispose() {
    // Release tất cả ghế đã chọn khi user thoát trang
    _releaseAllSelectedSeats();
    _debounceTimer?.cancel();
    _seatWidgetCache.clear();
    super.dispose();
  }

  // Release tất cả ghế đã chọn
  Future<void> _releaseAllSelectedSeats() async {
    if (_selectedSeats.isNotEmpty) {
      try {
        final authController = Get.find<AuthController>();
        final userId =
            authController.isLoggedIn ? authController.user?.id : null;

        await _showtimeService.releaseSeats(
          widget.showtime.id,
          _selectedSeats.toList(),
          userId: userId,
        );
      } catch (e) {
        print('Error releasing all selected seats: $e');
      }
    }
  }

  Future<void> _loadScreen() async {
    try {
      _isLoading.value = true;
      final screen =
          await _screenService.getScreenById(widget.showtime.screenId);
      _screen.value = screen;
    } catch (e) {
      Get.snackbar(
        'Lỗi',
        'Không thể tải thông tin phòng chiếu: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.withOpacity(0.7),
        colorText: Colors.white,
      );
    } finally {
      _isLoading.value = false;
    }
  }

  void _toggleSeat(String seatId, SeatType seatType) {
    if (_selectedSeats.contains(seatId)) {
      // User bỏ chọn ghế - release reservation ngay lập tức
      _selectedSeats.remove(seatId);
      _releaseReservationRealtime(seatId);
    } else {
      if (_selectedSeats.length < 8) {
        // Maximum 8 seats
        _selectedSeats.add(seatId);
        _reserveSeatRealtime(seatId);
      } else {
        Get.snackbar(
          'Thông báo',
          'Bạn chỉ có thể chọn tối đa 8 ghế',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.orange.withOpacity(0.7),
          colorText: Colors.white,
        );
        return;
      }
    }

    _calculateTotalPrice();
    _clearCacheForSeat(seatId); // Clear cache cho ghế này
  }

  // Reserve ghế ngay khi user chọn
  Future<void> _reserveSeatRealtime(String seatId) async {
    try {
      final authController = Get.find<AuthController>();
      final userId = authController.isLoggedIn ? authController.user?.id : null;

      await _showtimeService.reserveSeats(
        widget.showtime.id,
        [seatId],
        userId: userId,
      );
    } catch (e) {
      // Nếu không reserve được, bỏ chọn ghế
      _selectedSeats.remove(seatId);
      _calculateTotalPrice();

      String errorMessage = 'Không thể chọn ghế';
      if (e.toString().contains('đã được đặt')) {
        errorMessage = 'Ghế đã được đặt bởi người khác';
      } else if (e.toString().contains('đang được giữ')) {
        errorMessage = 'Ghế đang được giữ bởi người khác';
      }

      Get.snackbar(
        'Không thể chọn ghế',
        errorMessage,
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.withOpacity(0.7),
        colorText: Colors.white,
        duration: const Duration(seconds: 3),
      );

      // Refresh để cập nhật trạng thái ghế
      _refreshShowtimeData();
    }
  }

  // Release ghế ngay khi user bỏ chọn
  Future<void> _releaseReservationRealtime(String seatId) async {
    try {
      final authController = Get.find<AuthController>();
      final userId = authController.isLoggedIn ? authController.user?.id : null;

      await _showtimeService.releaseSeats(
        widget.showtime.id,
        [seatId],
        userId: userId,
      );
    } catch (e) {
      print('Error releasing seat $seatId: $e');
      // Không hiển thị lỗi cho user khi release vì không ảnh hưởng UX
    }
  }

  void _calculateTotalPrice() {
    double total = 0.0;

    for (final seatId in _selectedSeats) {
      // Find seat type from screen layout
      SeatType seatType = SeatType.standard;
      if (_screen.value != null) {
        for (final row in _screen.value!.seatLayout) {
          for (final seat in row.seats) {
            if ('${row.row}${seat.number}' == seatId) {
              seatType = seat.type;
              break;
            }
          }
        }
      }

      total += widget.showtime.pricing.getPriceForSeatType(seatType.name);
    }

    _totalPrice.value = total;
  }

  Future<void> _refreshShowtimeData() async {
    try {
      // Clear selected seats as they might no longer be available
      _selectedSeats.clear();
      _totalPrice.value = 0.0;

      // Reload screen data to get updated seat status
      await _loadScreen();

      // Clean up expired reservations
      await _showtimeService.cleanupExpiredReservations(widget.showtime.id);
    } catch (e) {
      print('Error refreshing showtime data: $e');
    }
  }

  Future<void> _proceedToPayment() async {
    if (_selectedSeats.isEmpty) {
      Get.snackbar(
        'Thông báo',
        'Vui lòng chọn ít nhất một ghế',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.orange.withOpacity(0.7),
        colorText: Colors.white,
      );
      return;
    }

    // Ghế đã được reserve realtime khi user chọn, chỉ cần navigate đến payment
    final result = await Get.to(() => PaymentPage(
          movie: widget.movie,
          showtime: widget.showtime,
          theater: widget.theater,
          screen: _screen.value!,
          selectedSeats: _selectedSeats,
          totalPrice: _totalPrice.value,
        ));

    // Nếu user back từ payment page mà không thanh toán thành công
    // thì ghế vẫn được giữ và user có thể tiếp tục chọn
    if (result == null) {
      // User back từ payment page, refresh để cập nhật trạng thái
      _refreshShowtimeData();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        height: MediaQuery.of(context).size.height,
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            colors: [
              Color(0xff2B5876),
              Color(0xff4E4376),
            ],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              // App Bar
              Container(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    IconButton(
                      onPressed: () => Get.back(),
                      icon: const Icon(Icons.arrow_back, color: Colors.white),
                    ),
                    Expanded(
                      child: Text(
                        'Chọn Ghế',
                        style: GoogleFonts.mulish(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              // Movie & Showtime Info
              Container(
                margin: const EdgeInsets.symmetric(horizontal: 16),
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.white.withOpacity(0.2)),
                ),
                child: Column(
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            widget.movie.title,
                            style: GoogleFonts.mulish(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: Colors.blue.withOpacity(0.2),
                            borderRadius: BorderRadius.circular(8),
                            border:
                                Border.all(color: Colors.blue.withOpacity(0.5)),
                          ),
                          child: Text(
                            _screen.value?.type.displayName ?? '',
                            style: GoogleFonts.mulish(
                              fontSize: 10,
                              color: Colors.blue[200],
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          widget.theater.name,
                          style: GoogleFonts.mulish(
                            fontSize: 12,
                            color: Colors.white.withOpacity(0.7),
                          ),
                        ),
                        Text(
                          '${widget.showtime.date} • ${widget.showtime.time}',
                          style: GoogleFonts.mulish(
                            fontSize: 12,
                            color: Colors.white.withOpacity(0.7),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 16),

              // Screen
              Container(
                margin: const EdgeInsets.symmetric(horizontal: 16),
                child: Column(
                  children: [
                    Container(
                      width: double.infinity,
                      height: 4,
                      decoration: BoxDecoration(
                        gradient: const LinearGradient(
                          colors: [
                            Colors.transparent,
                            Colors.white,
                            Colors.transparent
                          ],
                        ),
                        borderRadius: BorderRadius.circular(2),
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'MÀN HÌNH',
                      style: GoogleFonts.mulish(
                        fontSize: 12,
                        color: Colors.white.withOpacity(0.7),
                        letterSpacing: 2,
                      ),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 24),

              // Seat Map
              Expanded(
                child: Obx(() {
                  if (_isLoading.value || _screen.value == null) {
                    return const Center(
                      child: CircularProgressIndicator(color: Colors.white),
                    );
                  }

                  return _buildOptimizedSeatMap();
                }),
              ),

              // Legend
              Container(
                margin: const EdgeInsets.all(16),
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.white.withOpacity(0.2)),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: [
                    _buildLegendItem('Trống', Colors.white.withOpacity(0.3)),
                    _buildLegendItem('Đã chọn', Colors.blue),
                    _buildLegendItem('Đã đặt', Colors.red),
                    _buildLegendItem('VIP', Colors.amber),
                  ],
                ),
              ),

              // Bottom Bar
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.1),
                  border: Border(
                    top: BorderSide(color: Colors.white.withOpacity(0.2)),
                  ),
                ),
                child: Row(
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Obx(() => Text(
                                'Đã chọn: ${_selectedSeats.length} ghế',
                                style: GoogleFonts.mulish(
                                  fontSize: 14,
                                  color: Colors.white.withOpacity(0.7),
                                ),
                              )),
                          const SizedBox(height: 4),
                          Obx(() => Text(
                                '${_totalPrice.value.toStringAsFixed(0)} VNĐ',
                                style: GoogleFonts.mulish(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.white,
                                ),
                              )),
                        ],
                      ),
                    ),
                    const SizedBox(width: 16),
                    Obx(() => ElevatedButton(
                          onPressed: _selectedSeats.isNotEmpty
                              ? _proceedToPayment
                              : null,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.blue,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(
                                horizontal: 24, vertical: 12),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                          child: Text(
                            'Tiếp tục',
                            style: GoogleFonts.mulish(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        )),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Tối ưu hóa render sơ đồ ghế cho rạp có nhiều ghế
  Widget _buildOptimizedSeatMap() {
    final screen = _screen.value!;
    final totalRows = screen.seatLayout.length;

    // Nếu ít hơn 15 hàng, render bình thường
    if (totalRows <= 15) {
      return SingleChildScrollView(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        child: Column(
          children: screen.seatLayout.map((row) => _buildSeatRow(row)).toList(),
        ),
      );
    }

    // Với nhiều hàng ghế, sử dụng ListView.builder để tối ưu
    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: totalRows,
      itemBuilder: (context, index) {
        return _buildSeatRow(screen.seatLayout[index]);
      },
      // Tối ưu hiệu suất
      cacheExtent: 500, // Cache 500px trước và sau viewport
      physics: const BouncingScrollPhysics(),
    );
  }

  Widget _buildSeatRow(SeatRowModel row) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Row label
          SizedBox(
            width: 20,
            child: Text(
              row.row,
              style: GoogleFonts.mulish(
                fontSize: 12,
                color: Colors.white.withOpacity(0.7),
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
          ),
          const SizedBox(width: 8),

          // Seats - Tối ưu hóa render
          Expanded(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: _buildOptimizedSeats(row),
            ),
          ),

          const SizedBox(width: 8),

          // Row label (right)
          SizedBox(
            width: 20,
            child: Text(
              row.row,
              style: GoogleFonts.mulish(
                fontSize: 12,
                color: Colors.white.withOpacity(0.7),
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLegendItem(String label, Color color) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 12,
          height: 12,
          decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.circular(2),
          ),
        ),
        const SizedBox(width: 4),
        Text(
          label,
          style: GoogleFonts.mulish(
            fontSize: 10,
            color: Colors.white.withOpacity(0.7),
          ),
        ),
      ],
    );
  }

  // Tối ưu hóa render ghế trong một hàng
  List<Widget> _buildOptimizedSeats(SeatRowModel row) {
    final seats = <Widget>[];

    // Nếu hàng có ít ghế, render bình thường
    if (row.seats.length <= 20) {
      for (final seat in row.seats) {
        seats.add(_buildSeatWidget(row.row, seat));
      }
      return seats;
    }

    // Với nhiều ghế, sử dụng Wrap để tự động xuống hàng
    return [
      Flexible(
        child: Wrap(
          alignment: WrapAlignment.center,
          spacing: 4,
          runSpacing: 2,
          children:
              row.seats.map((seat) => _buildSeatWidget(row.row, seat)).toList(),
        ),
      ),
    ];
  }

  // Widget ghế được tối ưu hóa với caching
  Widget _buildSeatWidget(String rowLetter, SeatModel seat) {
    final seatId = '$rowLetter${seat.number}';
    final isSelected = _selectedSeats.contains(seatId);
    final isBooked = widget.showtime.isSeatBooked(seatId);
    final isReserved = widget.showtime.isSeatReserved(seatId);
    final isAvailable = seat.isAvailable && !isBooked && !isReserved;

    // Tạo cache key dựa trên trạng thái ghế
    final cacheKey =
        '$seatId-${seat.type.name}-$isSelected-$isBooked-$isReserved-$isAvailable';

    // Kiểm tra cache trước (chỉ cache cho ghế không thay đổi trạng thái)
    if (!isSelected && _seatWidgetCache.containsKey(cacheKey)) {
      return _seatWidgetCache[cacheKey]!;
    }

    final seatWidget = GestureDetector(
      onTap: isAvailable ? () => _toggleSeat(seatId, seat.type) : null,
      child: Container(
        width: 24,
        height: 24,
        margin: const EdgeInsets.symmetric(horizontal: 1),
        decoration: BoxDecoration(
          color: _getSeatColor(
              seat.type, isSelected, isBooked, isReserved, isAvailable),
          borderRadius: BorderRadius.circular(4),
          border: Border.all(
            color: isSelected ? Colors.blue : Colors.transparent,
            width: 2,
          ),
        ),
        child: Center(
          child: Text(
            seat.number,
            style: GoogleFonts.mulish(
              fontSize: 8,
              color: isAvailable || isSelected
                  ? Colors.white
                  : Colors.white.withOpacity(0.5),
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ),
    );

    // Cache widget nếu không phải ghế được chọn và cache chưa quá lớn
    if (!isSelected && _seatWidgetCache.length < 300) {
      _seatWidgetCache[cacheKey] = seatWidget;
    }

    return seatWidget;
  }

  // Clear cache cho một ghế cụ thể khi trạng thái thay đổi
  void _clearCacheForSeat(String seatId) {
    _seatWidgetCache.removeWhere((key, value) => key.startsWith(seatId));
  }

  // Clear toàn bộ cache khi cần
  void _clearAllCache() {
    _seatWidgetCache.clear();
  }

  Color _getSeatColor(SeatType seatType, bool isSelected, bool isBooked,
      bool isReserved, bool isAvailable) {
    if (isBooked || isReserved) {
      return AppColors.seatBooked.withOpacity(0.7);
    }

    if (isSelected) {
      return AppColors.seatSelected;
    }

    if (!isAvailable) {
      return AppColors.textDisabled.withOpacity(0.3);
    }

    switch (seatType) {
      case SeatType.vip:
        return AppColors.seatVip.withOpacity(0.7);
      case SeatType.couple:
        return AppColors.seatCouple.withOpacity(0.7);
      case SeatType.disabled:
        return AppColors.seatDisabled.withOpacity(0.7);
      default:
        return AppColors.seatAvailable;
    }
  }
}
