/// Controller phim để quản lý dữ liệu phim từ Firebase và TMDB API
import 'package:get/get.dart';
import '../models/movie_model.dart';
import '../services/movie_service.dart';
import '../services/firebase_movie_service.dart';

class MovieController extends GetxController {
  // Các instance service để quản lý dữ liệu phim
  final MovieService _movieService = MovieService();
  final FirebaseMovieService _firebaseMovieService = FirebaseMovieService();

  // Danh sách phim từ TMDB API (hỗ trợ cũ)
  final RxList<Movie> popularMovies = <Movie>[].obs;
  final RxList<Movie> upcomingMovies = <Movie>[].obs;
  final RxList<Movie> searchResults = <Movie>[].obs;
  final Rx<Movie?> selectedMovie = Rx<Movie?>(null);

  // Danh sách phim từ Firebase (nguồn dữ liệu ch<PERSON>h)
  final RxList<Movie> allMovies = <Movie>[].obs; // Tất cả phim
  final RxList<Movie> homeBannerMovies = <Movie>[].obs; // Banner trang chủ
  final RxList<Movie> splashBannerMovies =
      <Movie>[].obs; // Banner màn hình chào
  final RxList<Movie> nowPlayingMovies = <Movie>[].obs; // Phim đang chiếu
  final RxList<Movie> upcomingFirebaseMovies = <Movie>[].obs; // Phim sắp chiếu
  final RxList<Movie> endedMovies = <Movie>[].obs; // Phim đã kết thúc
  final RxList<Movie> bookableMovies = <Movie>[].obs; // Phim có thể đặt vé

  // Trạng thái loading cho các thao tác khác nhau
  final RxBool isLoadingPopular = false.obs;
  final RxBool isLoadingUpcoming = false.obs;
  final RxBool isLoadingSearch = false.obs;
  final RxBool isLoadingMovieDetails = false.obs;
  final RxBool isLoadingFirebaseMovies = false.obs;
  final RxBool isSubmitting = false.obs;

  // Xử lý lỗi và trạng thái tìm kiếm
  final RxString errorMessage = ''.obs;
  final RxString searchQuery = ''.obs;

  @override
  void onInit() {
    super.onInit();
    // Khởi tạo chỉ với dữ liệu Firebase (nguồn dữ liệu chính)
    fetchAllFirebaseMovies();
    fetchHomeBannerMovies();
    fetchSplashBannerMovies();
  }

  // ===== CÁC PHƯƠNG THỨC TMDB API (Hỗ trợ cũ) =====

  /// Lấy phim phổ biến từ TMDB API (phương thức cũ)
  Future<void> fetchPopularMovies() async {
    try {
      isLoadingPopular.value = true;
      errorMessage.value = '';

      final movies = await _movieService.getPopularMovies();
      popularMovies.value = movies;
    } catch (e) {
      errorMessage.value = 'Failed to load popular movies: $e';
    } finally {
      isLoadingPopular.value = false;
    }
  }

  /// Fetch upcoming movies from TMDB API (legacy method)
  /// Lấy phim sắp chiếu từ TMDB API (phương thức cũ)
  Future<void> fetchUpcomingMovies() async {
    try {
      isLoadingUpcoming.value = true;
      errorMessage.value = '';

      final movies = await _movieService.getUpcomingMovies();
      upcomingMovies.value = movies;
    } catch (e) {
      errorMessage.value = 'Failed to load upcoming movies: $e';
    } finally {
      isLoadingUpcoming.value = false;
    }
  }

  /// Search movies using TMDB API (legacy method)
  /// Tìm kiếm phim bằng TMDB API (phương thức cũ)
  Future<void> searchMovies(String query) async {
    if (query.isEmpty) {
      searchResults.clear();
      return;
    }

    try {
      isLoadingSearch.value = true;
      errorMessage.value = '';
      searchQuery.value = query;

      final movies = await _movieService.searchMovies(query);
      searchResults.value = movies;
    } catch (e) {
      errorMessage.value = 'Failed to search movies: $e';
    } finally {
      isLoadingSearch.value = false;
    }
  }

  /// Get detailed movie information with Firebase priority
  /// Lấy thông tin chi tiết phim với ưu tiên Firebase
  Future<Movie?> getMovieDetails(int movieId) async {
    try {
      isLoadingMovieDetails.value = true;
      errorMessage.value = '';

      // Try Firebase first (primary data source)
      // Thử Firebase trước (nguồn dữ liệu chính)
      Movie? movie = await _firebaseMovieService.getMovieByMovieId(movieId);

      // Fallback to TMDB if not found in Firebase
      // Dự phòng TMDB nếu không tìm thấy trong Firebase
      if (movie == null) {
        try {
          movie = await _movieService.getMovieDetails(movieId);
        } catch (e) {
          // TMDB fallback failed, but that's acceptable
          // TMDB dự phòng thất bại, nhưng điều đó có thể chấp nhận được
          print('TMDB fallback failed: $e');
        }
      }

      selectedMovie.value = movie;
      return movie;
    } catch (e) {
      errorMessage.value = 'Failed to load movie details: $e';
      return null;
    } finally {
      isLoadingMovieDetails.value = false;
    }
  }

  /// Clear search results and query
  /// Xóa kết quả tìm kiếm và truy vấn
  void clearSearch() {
    searchResults.clear();
    searchQuery.value = '';
  }

  // ===== FIREBASE MOVIE METHODS (Primary Data Source) =====
  // ===== CÁC PHƯƠNG THỨC PHIM FIREBASE (Nguồn dữ liệu chính) =====

  /// Fetch all movies from Firebase and categorize by status
  /// Lấy tất cả phim từ Firebase và phân loại theo trạng thái
  Future<void> fetchAllFirebaseMovies() async {
    try {
      isLoadingFirebaseMovies.value = true;
      errorMessage.value = '';

      final movies = await _firebaseMovieService.getMovies();

      // Use Future.microtask to avoid setState during build
      // Sử dụng Future.microtask để tránh setState trong quá trình build
      await Future.microtask(() {
        allMovies.value = movies;

        // Categorize movies by their current status
        // Phân loại phim theo trạng thái hiện tại
        nowPlayingMovies.value =
            movies.where((m) => m.status == MovieStatus.nowPlaying).toList();
        upcomingFirebaseMovies.value =
            movies.where((m) => m.status == MovieStatus.upcoming).toList();
        endedMovies.value =
            movies.where((m) => m.status == MovieStatus.ended).toList();
      });
    } catch (e) {
      await Future.microtask(() {
        errorMessage.value = 'Failed to load movies: $e';
      });
    } finally {
      await Future.microtask(() {
        isLoadingFirebaseMovies.value = false;
      });
    }
  }

  /// Fetch movies designated as home screen banners
  /// Lấy các phim được chỉ định làm banner trang chủ
  Future<void> fetchHomeBannerMovies() async {
    try {
      errorMessage.value = '';
      final movies = await _firebaseMovieService.getHomeBannerMovies();
      await Future.microtask(() {
        homeBannerMovies.value = movies;
      });
    } catch (e) {
      await Future.microtask(() {
        errorMessage.value = 'Failed to load home banner movies: $e';
      });
    }
  }

  /// Fetch movies designated as splash screen banners
  /// Lấy các phim được chỉ định làm banner màn hình chào
  Future<void> fetchSplashBannerMovies() async {
    try {
      errorMessage.value = '';
      final movies = await _firebaseMovieService.getSplashBannerMovies();
      await Future.microtask(() {
        splashBannerMovies.value = movies;
      });
    } catch (e) {
      await Future.microtask(() {
        errorMessage.value = 'Failed to load splash banner movies: $e';
      });
    }
  }

  /// Fetch only bookable movies (movies with showtimes)
  /// Lấy chỉ những phim có thể đặt vé (phim có lịch chiếu)
  Future<void> fetchBookableMovies() async {
    try {
      isLoadingFirebaseMovies.value = true;
      errorMessage.value = '';

      print('MovieController: Starting to fetch bookable movies');
      final movies = await _firebaseMovieService.getBookableMovies();
      print(
          'MovieController: Fetched ${movies.length} bookable movies from Firebase');

      await Future.microtask(() {
        bookableMovies.value = movies;
      });

      print('MovieController: Updated bookable movies list');
    } catch (e) {
      print('MovieController: Error fetching bookable movies: $e');
      await Future.microtask(() {
        errorMessage.value = 'Failed to load bookable movies: $e';
      });
    } finally {
      await Future.microtask(() {
        isLoadingFirebaseMovies.value = false;
      });
    }
  }

  /// Search movies in Firebase database by title
  /// Tìm kiếm phim trong cơ sở dữ liệu Firebase theo tiêu đề
  Future<void> searchFirebaseMovies(String query) async {
    print('MovieController: searchFirebaseMovies called with query: "$query"');

    if (query.isEmpty) {
      print('MovieController: Query is empty, clearing search results');
      searchResults.clear();
      return;
    }

    try {
      isLoadingSearch.value = true;
      errorMessage.value = '';
      searchQuery.value = query;

      print('MovieController: Starting search for query: "$query"');
      final movies = await _firebaseMovieService.searchMovies(query);

      print('MovieController: Search completed. Found ${movies.length} movies');
      searchResults.value = movies;

      // Log first few results for debugging purposes
      // Ghi log một vài kết quả đầu tiên để debug
      if (movies.isNotEmpty) {
        print('MovieController: First few results:');
        for (int i = 0; i < movies.length && i < 3; i++) {
          print('  - ${movies[i].title}');
        }
      } else {
        print('MovieController: No movies found for query "$query"');
      }
    } catch (e) {
      print('MovieController: Error searching movies: $e');
      errorMessage.value = 'Failed to search movies: $e';
    } finally {
      isLoadingSearch.value = false;
    }
  }

  // ===== ADMIN METHODS (Movie Management) =====
  // ===== CÁC PHƯƠNG THỨC ADMIN (Quản lý phim) =====

  /// Add a new movie to Firebase (admin only)
  /// Thêm phim mới vào Firebase (chỉ admin)
  Future<bool> addMovie(Movie movie) async {
    try {
      isSubmitting.value = true;
      errorMessage.value = '';

      await _firebaseMovieService.addMovie(movie);
      await fetchAllFirebaseMovies(); // Refresh movie list / Làm mới danh sách phim
      return true;
    } catch (e) {
      errorMessage.value = 'Failed to add movie: $e';
      return false;
    } finally {
      isSubmitting.value = false;
    }
  }

  /// Update existing movie information (admin only)
  /// Cập nhật thông tin phim hiện có (chỉ admin)
  Future<bool> updateMovie(Movie movie) async {
    try {
      isSubmitting.value = true;
      errorMessage.value = '';

      await _firebaseMovieService.updateMovie(movie);
      await fetchAllFirebaseMovies(); // Refresh main movie list / Làm mới danh sách phim chính

      // Refresh banner lists if banner status changed
      // Làm mới danh sách banner nếu trạng thái banner thay đổi
      if (movie.isHomeBanner) {
        await fetchHomeBannerMovies();
      }
      if (movie.isSplashBanner) {
        await fetchSplashBannerMovies();
      }

      return true;
    } catch (e) {
      errorMessage.value = 'Failed to update movie: $e';
      return false;
    } finally {
      isSubmitting.value = false;
    }
  }

  /// Delete a movie from Firebase (admin only)
  /// Xóa phim khỏi Firebase (chỉ admin)
  Future<bool> deleteMovie(String movieId) async {
    try {
      isSubmitting.value = true;
      errorMessage.value = '';

      await _firebaseMovieService.deleteMovie(movieId);
      await fetchAllFirebaseMovies(); // Refresh main list / Làm mới danh sách chính
      await fetchHomeBannerMovies(); // Refresh banner lists / Làm mới danh sách banner
      await fetchSplashBannerMovies();

      return true;
    } catch (e) {
      errorMessage.value = 'Failed to delete movie: $e';
      return false;
    } finally {
      isSubmitting.value = false;
    }
  }

  /// Toggle movie's home banner status (admin only)
  /// Chuyển đổi trạng thái banner trang chủ của phim (chỉ admin)
  Future<bool> toggleHomeBanner(String movieId, bool isHomeBanner,
      {int? order}) async {
    try {
      errorMessage.value = '';
      await _firebaseMovieService.toggleHomeBanner(movieId, isHomeBanner,
          order: order);
      await fetchHomeBannerMovies();
      await fetchAllFirebaseMovies();
      return true;
    } catch (e) {
      errorMessage.value = 'Failed to toggle home banner: $e';
      return false;
    }
  }

  /// Toggle movie's splash banner status (admin only)
  /// Chuyển đổi trạng thái banner màn hình chào của phim (chỉ admin)
  Future<bool> toggleSplashBanner(String movieId, bool isSplashBanner,
      {int? order}) async {
    try {
      errorMessage.value = '';
      await _firebaseMovieService.toggleSplashBanner(movieId, isSplashBanner,
          order: order);
      await fetchSplashBannerMovies();
      await fetchAllFirebaseMovies();
      return true;
    } catch (e) {
      errorMessage.value = 'Failed to toggle splash banner: $e';
      return false;
    }
  }
}
